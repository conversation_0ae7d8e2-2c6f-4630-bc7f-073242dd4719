# Securinest Audit API Testing Guide

## Setup Instructions

### 1. Database Setup
1. Run your main database migration scripts first
2. Execute the test data script:
   ```sql
   -- Run audit-test-data.sql
   ```

### 2. Postman Setup
1. Import `Audit_API_Collection.postman_collection.json` into Postman
2. Import `Audit_API_Environment.postman_environment.json` as environment
3. Select the "Securinest Audit API - Local" environment

### 3. Application Setup
1. Start your Spring Boot application
2. Ensure it's running on `http://localhost:8080`

## Test Data Overview

### Users & Roles
- **Admin User** (`550e8400-e29b-41d4-a716-************`): OWNER role
- **Auditor User** (`550e8400-e29b-41d4-a716-************`): AUDITOR role  
- **Viewer User** (`550e8400-e29b-41d4-a716-************`): VIEWER role

### Test Tenant
- **Tenant ID**: `550e8400-e29b-41d4-a716-************`
- **Name**: "Test Company"
- **Slug**: "test-company"

### Services & Environments
- **Production Environment**: `550e8400-e29b-41d4-a716-446655440020`
- **Staging Environment**: `550e8400-e29b-41d4-a716-446655440021`
- **User API Service**: `550e8400-e29b-41d4-a716-446655440030`
- **Payment Service**: `550e8400-e29b-41d4-a716-446655440031`

### Audit Log Entries
- 18 audit log entries spanning 30 days
- Various action types: TENANT_CREATED, MEMBER_ADDED, POLICY_CREATED, etc.
- Different target types: TENANT, SERVICE, ENVIRONMENT, POLICY, etc.

### Audit Packs
- **SUCCEEDED**: `550e8400-e29b-41d4-a716-446655440070` (completed pack)
- **RUNNING**: `550e8400-e29b-41d4-a716-446655440071` (in progress)
- **QUEUED**: `550e8400-e29b-41d4-a716-446655440072` (waiting)
- **FAILED**: `550e8400-e29b-41d4-a716-446655440073` (failed pack)

## Test Scenarios

### 1. Audit Logs API Tests

#### ✅ Happy Path Tests
1. **Get All Audit Logs (Admin)** - Should return paginated audit logs
2. **Get Audit Logs with Filters** - Test action and targetType filters
3. **Get Audit Logs by Actor** - Filter by specific user
4. **Get Audit Logs with Date Range** - Test time-based filtering

#### ❌ Error Cases
1. **Unauthorized Access (Viewer)** - Should return 403 Forbidden
2. **No Auth Header** - Should return 401 Unauthorized
3. **Invalid Date Range** - Should return 400 Bad Request

### 2. Audit Packs API Tests

#### ✅ Happy Path Tests
1. **Create Audit Pack** - Create new audit pack with valid scope
2. **Create Audit Pack - Staging Only** - Test different scope
3. **List All Audit Packs** - Get paginated list
4. **List by Status Filter** - Filter by SUCCEEDED status
5. **Get Specific Audit Pack** - Retrieve individual pack

#### ❌ Error Cases
1. **Invalid Scope** - Empty services/envs arrays
2. **Viewer Role (Forbidden)** - VIEWER cannot create packs
3. **Pack Not Found** - Non-existent pack ID

## Expected Response Formats

### Audit Log Entry Response
```json
{
  "id": "1",
  "tenantId": "550e8400-e29b-41d4-a716-************",
  "actorUserId": "550e8400-e29b-41d4-a716-************",
  "action": "TENANT_CREATED",
  "targetType": "TENANT",
  "targetId": "550e8400-e29b-41d4-a716-************",
  "ts": "2024-01-01T10:00:00Z",
  "diff": "{\"name\": \"Test Company\", \"region\": \"us\"}"
}
```

### Audit Pack Response
```json
{
  "id": "550e8400-e29b-41d4-a716-446655440070",
  "tenantId": "550e8400-e29b-41d4-a716-************",
  "scope": {
    "services": ["550e8400-e29b-41d4-a716-446655440030"],
    "envs": ["550e8400-e29b-41d4-a716-446655440020"],
    "dateRange": {
      "from": "2024-01-01T00:00:00Z",
      "to": "2024-01-31T23:59:59Z"
    }
  },
  "status": "SUCCEEDED",
  "manifestHash": "sha256:abc123def456...",
  "artifactUri": "https://storage.example.com/audit-packs/pack-70.zip",
  "aiActVersion": "1.0.0",
  "createdBy": "550e8400-e29b-41d4-a716-************",
  "createdAt": "2024-01-15T08:00:00Z",
  "finishedAt": "2024-01-15T09:00:00Z",
  "signatureB64": "signature_base64_string_here"
}
```

## Additional SQL Queries for Testing

### Check Audit Log Filtering
```sql
-- Get logs by action type
SELECT action, target_type, COUNT(*) 
FROM securinest.audit_log_entry 
WHERE tenant_id = '550e8400-e29b-41d4-a716-************'
GROUP BY action, target_type
ORDER BY COUNT(*) DESC;

-- Get logs by actor
SELECT actor_user_id, COUNT(*) as action_count
FROM securinest.audit_log_entry 
WHERE tenant_id = '550e8400-e29b-41d4-a716-************'
GROUP BY actor_user_id;

-- Get recent logs (last 7 days)
SELECT action, target_type, ts
FROM securinest.audit_log_entry 
WHERE tenant_id = '550e8400-e29b-41d4-a716-************'
  AND ts >= NOW() - INTERVAL '7 days'
ORDER BY ts DESC;
```

### Check Audit Pack Status Distribution
```sql
-- Count packs by status
SELECT status, COUNT(*) as pack_count
FROM securinest.audit_pack 
WHERE tenant_id = '550e8400-e29b-41d4-a716-************'
GROUP BY status;

-- Get pack creation timeline
SELECT id, status, created_at, finished_at,
       CASE 
         WHEN finished_at IS NOT NULL 
         THEN EXTRACT(EPOCH FROM (finished_at - created_at))/60 
         ELSE NULL 
       END as duration_minutes
FROM securinest.audit_pack 
WHERE tenant_id = '550e8400-e29b-41d4-a716-************'
ORDER BY created_at DESC;
```

## Role-Based Access Testing

### OWNER/ADMIN Users Can:
- ✅ View all audit logs
- ✅ Create audit packs
- ✅ List audit packs
- ✅ View specific audit packs

### AUDITOR Users Can:
- ✅ View all audit logs
- ✅ Create audit packs
- ✅ List audit packs
- ✅ View specific audit packs

### POLICY_ADMIN Users Can:
- ✅ View all audit logs (with potential redaction)
- ❌ Cannot create audit packs
- ❌ Cannot list audit packs
- ❌ Cannot view audit packs

### VIEWER Users Cannot:
- ❌ View audit logs (403 Forbidden)
- ❌ Create audit packs (403 Forbidden)
- ❌ List audit packs (403 Forbidden)
- ❌ View audit packs (403 Forbidden)

## Performance Testing

### Large Dataset Queries
```sql
-- Add more test data for performance testing
INSERT INTO securinest.audit_log_entry (id, tenant_id, actor_user_id, action, target_type, target_id, ts, diff, created_at)
SELECT 
  generate_series(100, 1099) as id,
  '550e8400-e29b-41d4-a716-************' as tenant_id,
  '550e8400-e29b-41d4-a716-************' as actor_user_id,
  'POLICY_UPDATED' as action,
  'POLICY' as target_type,
  '550e8400-e29b-41d4-a716-446655440040' as target_id,
  NOW() - (random() * INTERVAL '30 days') as ts,
  '{"field": "value"}' as diff,
  NOW() - (random() * INTERVAL '30 days') as created_at;
```

## Troubleshooting

### Common Issues
1. **401 Unauthorized**: Check X-Debug-UserId header is set
2. **403 Forbidden**: Verify user has correct role for the operation
3. **404 Not Found**: Ensure tenant membership exists
4. **400 Bad Request**: Check request body format and required fields

### Debug Queries
```sql
-- Check user roles
SELECT tm.tenant_id, tm.user_id, tm.role, ua.email
FROM securinest.tenant_member tm
JOIN securinest.user_account ua ON tm.user_id = ua.id
WHERE tm.tenant_id = '550e8400-e29b-41d4-a716-************';

-- Check recent API activity
SELECT action, COUNT(*) 
FROM securinest.audit_log_entry 
WHERE tenant_id = '550e8400-e29b-41d4-a716-************'
  AND ts >= NOW() - INTERVAL '1 hour'
GROUP BY action;
```
