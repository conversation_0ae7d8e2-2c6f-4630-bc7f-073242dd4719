{"info": {"name": "Securinest Control Plane API", "description": "Complete API collection for Securinest Control Plane backend services", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "version": "1.0.0"}, "variable": [{"key": "baseUrl", "value": "http://localhost:8080", "type": "string"}, {"key": "debugUserId", "value": "{{$guid}}", "type": "string"}, {"key": "tenantId", "value": "", "type": "string"}, {"key": "serviceId", "value": "", "type": "string"}, {"key": "envId", "value": "", "type": "string"}, {"key": "policyId", "value": "", "type": "string"}], "item": [{"name": "User Management", "item": [{"name": "Sign Up User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Request-Id", "value": "{{$guid}}"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"displayName\": \"<PERSON>\"\n}"}, "url": {"raw": "{{baseUrl}}/users/signup", "host": ["{{baseUrl}}"], "path": ["users", "signup"]}}}, {"name": "Get Current User", "request": {"method": "GET", "header": [{"key": "X-Debug-UserId", "value": "{{debugUserId}}"}], "url": {"raw": "{{baseUrl}}/users/me", "host": ["{{baseUrl}}"], "path": ["users", "me"]}}}, {"name": "Update Current User", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Debug-UserId", "value": "{{debugUserId}}"}, {"key": "If-Match", "value": "\"1\""}, {"key": "X-Request-Id", "value": "{{$guid}}"}], "body": {"mode": "raw", "raw": "{\n  \"displayName\": \"Updated Name\"\n}"}, "url": {"raw": "{{baseUrl}}/users/me", "host": ["{{baseUrl}}"], "path": ["users", "me"]}}}]}, {"name": "Tenant Management", "item": [{"name": "Create Tenant", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Debug-UserId", "value": "{{debugUserId}}"}, {"key": "X-Request-Id", "value": "{{$guid}}"}, {"key": "X-Idempotency-Key", "value": "{{$guid}}"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"My Company\",\n  \"slug\": \"my-company\",\n  \"region\": \"us\",\n  \"billingPlan\": \"free\"\n}"}, "url": {"raw": "{{baseUrl}}/tenants", "host": ["{{baseUrl}}"], "path": ["tenants"]}}}, {"name": "List My Tenants", "request": {"method": "GET", "header": [{"key": "X-Debug-UserId", "value": "{{debugUserId}}"}], "url": {"raw": "{{baseUrl}}/tenants?page=0&size=20", "host": ["{{baseUrl}}"], "path": ["tenants"], "query": [{"key": "page", "value": "0"}, {"key": "size", "value": "20"}]}}}, {"name": "Get Tenant", "request": {"method": "GET", "header": [{"key": "X-Debug-UserId", "value": "{{debugUserId}}"}], "url": {"raw": "{{baseUrl}}/tenants/{{tenantId}}", "host": ["{{baseUrl}}"], "path": ["tenants", "{{tenantId}}"]}}}, {"name": "Update Tenant", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Debug-UserId", "value": "{{debugUserId}}"}, {"key": "If-Match", "value": "\"1\""}, {"key": "X-Request-Id", "value": "{{$guid}}"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Company Name\",\n  \"billingPlan\": \"pro\"\n}"}, "url": {"raw": "{{baseUrl}}/tenants/{{tenantId}}", "host": ["{{baseUrl}}"], "path": ["tenants", "{{tenantId}}"]}}}]}, {"name": "Service Catalog", "item": [{"name": "Get Environments by Tenant", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/service-catalogs/tenants/{{tenantId}}/envs", "host": ["{{baseUrl}}"], "path": ["service-catalogs", "tenants", "{{tenantId}}", "envs"]}}}]}, {"name": "Compliance Policies", "item": [{"name": "Create Policy", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Debug-UserId", "value": "{{debugUserId}}"}, {"key": "X-Request-Id", "value": "{{$guid}}"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Data Protection Policy\",\n  \"description\": \"Policy to ensure data protection compliance\"\n}"}, "url": {"raw": "{{baseUrl}}/tenants/{{tenantId}}/policies", "host": ["{{baseUrl}}"], "path": ["tenants", "{{tenantId}}", "policies"]}}}, {"name": "List Policies", "request": {"method": "GET", "header": [{"key": "X-Debug-UserId", "value": "{{debugUserId}}"}], "url": {"raw": "{{baseUrl}}/tenants/{{tenantId}}/policies?state=DRAFT&page=0&size=20", "host": ["{{baseUrl}}"], "path": ["tenants", "{{tenantId}}", "policies"], "query": [{"key": "state", "value": "DRAFT"}, {"key": "page", "value": "0"}, {"key": "size", "value": "20"}]}}}, {"name": "Get Policy", "request": {"method": "GET", "header": [{"key": "X-Debug-UserId", "value": "{{debugUserId}}"}], "url": {"raw": "{{baseUrl}}/tenants/{{tenantId}}/policies/{{policyId}}", "host": ["{{baseUrl}}"], "path": ["tenants", "{{tenantId}}", "policies", "{{policyId}}"]}}}, {"name": "Update Policy", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Debug-UserId", "value": "{{debugUserId}}"}, {"key": "If-Match", "value": "\"1\""}, {"key": "X-Request-Id", "value": "{{$guid}}"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Policy Name\",\n  \"description\": \"Updated description\"\n}"}, "url": {"raw": "{{baseUrl}}/tenants/{{tenantId}}/policies/{{policyId}}", "host": ["{{baseUrl}}"], "path": ["tenants", "{{tenantId}}", "policies", "{{policyId}}"]}}}]}, {"name": "Policy Versions", "item": [{"name": "Create Policy Version", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Debug-UserId", "value": "{{debugUserId}}"}, {"key": "X-Request-Id", "value": "{{$guid}}"}], "body": {"mode": "raw", "raw": "{\n  \"json\": \"{\\\"version\\\": \\\"1.0\\\", \\\"metadata\\\": {\\\"name\\\": \\\"Sample Policy\\\"}, \\\"rules\\\": []}\"\n}"}, "url": {"raw": "{{baseUrl}}/tenants/{{tenantId}}/policies/{{policyId}}/versions", "host": ["{{baseUrl}}"], "path": ["tenants", "{{tenantId}}", "policies", "{{policyId}}", "versions"]}}}, {"name": "List Policy Versions", "request": {"method": "GET", "header": [{"key": "X-Debug-UserId", "value": "{{debugUserId}}"}], "url": {"raw": "{{baseUrl}}/tenants/{{tenantId}}/policies/{{policyId}}/versions?page=0&size=20", "host": ["{{baseUrl}}"], "path": ["tenants", "{{tenantId}}", "policies", "{{policyId}}", "versions"], "query": [{"key": "page", "value": "0"}, {"key": "size", "value": "20"}]}}}, {"name": "Get Policy Version", "request": {"method": "GET", "header": [{"key": "X-Debug-UserId", "value": "{{debugUserId}}"}], "url": {"raw": "{{baseUrl}}/tenants/{{tenantId}}/policies/{{policyId}}/versions/1", "host": ["{{baseUrl}}"], "path": ["tenants", "{{tenantId}}", "policies", "{{policyId}}", "versions", "1"]}}}, {"name": "Update Policy Version Status", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Debug-UserId", "value": "{{debugUserId}}"}, {"key": "X-Request-Id", "value": "{{$guid}}"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"ACTIVE\"\n}"}, "url": {"raw": "{{baseUrl}}/tenants/{{tenantId}}/policies/{{policyId}}/versions/1", "host": ["{{baseUrl}}"], "path": ["tenants", "{{tenantId}}", "policies", "{{policyId}}", "versions", "1"]}}}]}, {"name": "Policy Bundles", "item": [{"name": "<PERSON><PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Debug-UserId", "value": "{{debugUserId}}"}], "body": {"mode": "raw", "raw": "{\n  \"policyVersionId\": \"{{policyVersionId}}\",\n  \"serviceId\": \"{{serviceId}}\",\n  \"envId\": \"{{envId}}\",\n  \"enforcementMode\": \"SHADOW\",\n  \"lkgTtlSeconds\": 300,\n  \"sampling\": \"{\\\"rate\\\": 0.1}\"\n}"}, "url": {"raw": "{{baseUrl}}/tenants/{{tenantId}}/bundles/compile", "host": ["{{baseUrl}}"], "path": ["tenants", "{{tenantId}}", "bundles", "compile"]}}}, {"name": "List Bundles", "request": {"method": "GET", "header": [{"key": "X-Debug-UserId", "value": "{{debugUserId}}"}], "url": {"raw": "{{baseUrl}}/tenants/{{tenantId}}/bundles/services/{{serviceId}}/envs/{{envId}}/bundles?page=0&size=20", "host": ["{{baseUrl}}"], "path": ["tenants", "{{tenantId}}", "bundles", "services", "{{serviceId}}", "envs", "{{envId}}", "bundles"], "query": [{"key": "page", "value": "0"}, {"key": "size", "value": "20"}]}}}, {"name": "Deactivate Bundle", "request": {"method": "POST", "header": [{"key": "X-Debug-UserId", "value": "{{debugUserId}}"}], "url": {"raw": "{{baseUrl}}/tenants/{{tenantId}}/bundles/services/{{serviceId}}/envs/{{envId}}/bundles/{{bundleId}}/deactivate", "host": ["{{baseUrl}}"], "path": ["tenants", "{{tenantId}}", "bundles", "services", "{{serviceId}}", "envs", "{{envId}}", "bundles", "{{bundleId}}", "deactivate"]}}}]}, {"name": "SDK Bundles", "item": [{"name": "Get Current Bundle", "request": {"method": "GET", "header": [{"key": "If-None-Match", "value": "\"etag-value\"", "disabled": true}, {"key": "X-Env-Key", "value": "production"}, {"key": "X-Debug-TenantId", "value": "{{tenantId}}"}, {"key": "X-Debug-ServiceId", "value": "{{serviceId}}"}, {"key": "X-Debug-EnvId", "value": "{{envId}}"}], "url": {"raw": "{{baseUrl}}/sdk/bundles/current", "host": ["{{baseUrl}}"], "path": ["sdk", "bundles", "current"]}}}]}, {"name": "Signing Keys", "item": [{"name": "List Signing Keys", "request": {"method": "GET", "header": [{"key": "X-Debug-UserId", "value": "{{debugUserId}}"}], "url": {"raw": "{{baseUrl}}/tenants/{{tenantId}}/signing-keys", "host": ["{{baseUrl}}"], "path": ["tenants", "{{tenantId}}", "signing-keys"]}}}]}]}