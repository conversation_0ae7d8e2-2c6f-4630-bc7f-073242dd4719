{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Compliance Policy Schema", "type": "object", "properties": {"version": {"type": "string", "pattern": "^1\\.0$", "description": "Policy schema version"}, "metadata": {"type": "object", "properties": {"name": {"type": "string", "minLength": 1, "maxLength": 200}, "description": {"type": "string", "maxLength": 2000}, "tags": {"type": "array", "items": {"type": "string"}}}, "required": ["name"]}, "rules": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "pattern": "^[a-zA-Z0-9_-]+$"}, "name": {"type": "string", "minLength": 1, "maxLength": 100}, "description": {"type": "string", "maxLength": 500}, "enabled": {"type": "boolean", "default": true}, "severity": {"type": "string", "enum": ["LOW", "MEDIUM", "HIGH", "CRITICAL"]}, "conditions": {"type": "object", "properties": {"endpoints": {"type": "array", "items": {"type": "string"}}, "methods": {"type": "array", "items": {"type": "string", "enum": ["GET", "POST", "PUT", "DELETE", "PATCH", "HEAD", "OPTIONS"]}}, "headers": {"type": "object", "additionalProperties": {"type": "string"}}}}, "actions": {"type": "object", "properties": {"block": {"type": "boolean", "default": false}, "log": {"type": "boolean", "default": true}, "alert": {"type": "boolean", "default": false}}}}, "required": ["id", "name", "severity", "conditions", "actions"]}}}, "required": ["version", "metadata", "rules"], "additionalProperties": false}