package io.securinest.controlplaneapi.dto.finding;

import io.securinest.controlplaneapi.dto.shared.PageRequest;
import io.securinest.controlplaneapi.dto.shared.TimeRangeResponse;
import java.util.List;
import lombok.Builder;

@Builder
public record FindingQueryResponse(
    String serviceId,
    String envId,
    List<String> severity,
    List<String> status,
    TimeRangeResponse timeRange,
    PageRequest page
) {

}
