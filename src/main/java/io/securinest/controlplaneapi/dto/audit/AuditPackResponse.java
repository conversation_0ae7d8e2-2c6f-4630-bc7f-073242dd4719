package io.securinest.controlplaneapi.dto.audit;

import java.time.Instant;
import lombok.Builder;

@Builder
public record AuditPackResponse(
    String id,
    String tenantId,
    AuditPackScopeResponse scope,
    String status,
    String manifestHash,
    String artifactUri,
    String aiActVersion,
    String createdBy,
    Instant createdAt,
    Instant finishedAt,
    String signatureB64
) {

}
