package io.securinest.controlplaneapi.dto.audit;

import io.securinest.controlplaneapi.dto.shared.PageRequest;
import io.securinest.controlplaneapi.dto.shared.TimeRangeResponse;
import java.util.List;
import lombok.Builder;

@Builder
public record AuditQueryResponse(
    List<String> actions,
    String targetType,
    String targetId,
    String actorUserId,
    TimeRangeResponse timeRange,
    PageRequest page
) {

}
