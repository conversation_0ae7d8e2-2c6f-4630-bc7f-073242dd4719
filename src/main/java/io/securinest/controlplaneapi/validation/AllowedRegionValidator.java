package io.securinest.controlplaneapi.validation;

import io.securinest.controlplaneapi.service.TenantRulebook;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class AllowedRegionValidator implements ConstraintValidator<AllowedRegion, String> {

  private final TenantRulebook tenantRulebook;

  @Override
  public boolean isValid(String value, ConstraintValidatorContext context) {
    if (value == null) {
      return true;
    }

    try {
      tenantRulebook.validateRegion(value);
      return true;
    } catch (Exception e) {
      context.disableDefaultConstraintViolation();
      context.buildConstraintViolationWithTemplate(e.getMessage())
          .addConstraintViolation();
      return false;
    }
  }
}
