package io.securinest.controlplaneapi.exceptions.shared;

import lombok.Getter;
import org.springframework.http.HttpStatus;

@Getter
public class SecurinestException extends RuntimeException {

  private final HttpStatus httpStatus;

  public SecurinestException(HttpStatus httpStatus, String message) {
    super(message);
    this.httpStatus = httpStatus;
  }

  public SecurinestException(HttpStatus httpStatus, String message, Throwable cause) {
    super(message, cause);
    this.httpStatus = httpStatus;
  }
}
