package io.securinest.controlplaneapi.controller.identity;

import io.securinest.controlplaneapi.dto.identity.UserAccountCreateRequest;
import io.securinest.controlplaneapi.dto.identity.UserAccountResponse;
import io.securinest.controlplaneapi.projection.identity.UserPatch;
import io.securinest.controlplaneapi.service.UserService;
import io.securinest.controlplaneapi.util.shared.ResponseEntityUtils;
import io.securinest.controlplaneapi.util.shared.ValidationUtils;
import jakarta.validation.Valid;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
@RequestMapping(path = "/users", produces = "application/json")
public class UserController {

  private final UserService userService;

  @PostMapping(value = "/signup", consumes = "application/json")
  public ResponseEntity<UserAccountResponse> signUp(
      @RequestHeader(name = "X-Request-Id", required = false) String requestId,
      @RequestBody @Valid UserAccountCreateRequest body
  ) {
    UserAccountResponse response = userService.signUp(body, requestId);
    return ResponseEntityUtils.createResponseWithVersionedResource(response);
  }

  @GetMapping("/me")
  public ResponseEntity<UserAccountResponse> getMe(
      @RequestHeader(name = "X-Debug-UserId") UUID userId
  ) {
    UserAccountResponse response = userService.getMe(userId);
    return ResponseEntityUtils.getResponseWithVersionedResource(response);
  }

  @PutMapping(value = "/me", consumes = "application/json")
  public ResponseEntity<UserAccountResponse> updateMe(
      @RequestHeader(name = "X-Debug-UserId") UUID userId,
      @RequestHeader(name = "If-Match", required = false) String ifMatch,
      @RequestHeader(name = "X-Request-Id", required = false) String requestId,
      @RequestBody @Valid UserPatch body
  ) {
    if (ifMatch == null) {
      return ResponseEntityUtils.preconditionRequired(UserAccountResponse.class);
    }

    Long expectedVersion = ValidationUtils.parseIfMatch(ifMatch);
    UserAccountResponse response = userService.updateMe(userId, body, expectedVersion, requestId);

    return ResponseEntityUtils.updateResponseWithVersionedResource(response);
  }
}
