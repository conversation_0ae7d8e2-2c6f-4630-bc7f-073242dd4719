package io.securinest.controlplaneapi.controller.policy;

import io.securinest.controlplaneapi.dto.policy.CompliancePolicyCreateRequest;
import io.securinest.controlplaneapi.dto.policy.CompliancePolicyResponse;
import io.securinest.controlplaneapi.dto.policy.CompliancePolicyUpdateRequest;
import io.securinest.controlplaneapi.entity.shared.PolicyState;
import io.securinest.controlplaneapi.service.policy.CompliancePolicyService;
import io.securinest.controlplaneapi.util.shared.ResponseEntityUtils;
import io.securinest.controlplaneapi.util.shared.ValidationUtils;
import jakarta.validation.Valid;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
@RequestMapping("/tenants/{tenantId}/policies")
public class CompliancePoliciesController {

  private final CompliancePolicyService policyService;

  @PostMapping
  public ResponseEntity<CompliancePolicyResponse> createPolicy(
      @PathVariable UUID tenantId,
      @Valid @RequestBody CompliancePolicyCreateRequest request,
      @RequestHeader(name = "X-Debug-UserId") UUID debugUserId,
      @RequestHeader(name = "X-Request-Id", required = false) String requestId) {

    CompliancePolicyResponse policy = policyService.create(tenantId, request, debugUserId,
        requestId);
    return ResponseEntityUtils.created(policy);
  }

  @GetMapping
  public ResponseEntity<Page<CompliancePolicyResponse>> listPolicies(
      @PathVariable UUID tenantId,
      @RequestParam(required = false) String state,
      @RequestParam(defaultValue = "0") int page,
      @RequestParam(defaultValue = "20") int size,
      @RequestHeader(name = "X-Debug-UserId") UUID debugUserId) {

    PolicyState policyState = null;
    if (state != null && !state.isEmpty()) {
      try {
        policyState = PolicyState.valueOf(state.toUpperCase());
      } catch (IllegalArgumentException e) {
        throw new IllegalArgumentException("Invalid state: " + state);
      }
    }

    Pageable pageable = PageRequest.of(page, size);
    Page<CompliancePolicyResponse> policies = policyService.list(tenantId, policyState, pageable);

    return ResponseEntityUtils.ok(policies);
  }

  @GetMapping("/{policyId}")
  public ResponseEntity<CompliancePolicyResponse> getPolicy(
      @PathVariable UUID tenantId,
      @PathVariable UUID policyId,
      @RequestHeader(name = "X-Debug-UserId") UUID debugUserId) {

    CompliancePolicyResponse policy = policyService.get(tenantId, policyId, debugUserId);
    return ResponseEntityUtils.ok(policy);
  }

  @PutMapping("/{policyId}")
  public ResponseEntity<CompliancePolicyResponse> updatePolicy(
      @PathVariable UUID tenantId,
      @PathVariable UUID policyId,
      @Valid @RequestBody CompliancePolicyUpdateRequest request,
      @RequestHeader(name = "If-Match", required = false) String ifMatch,
      @RequestHeader(name = "X-Debug-UserId", required = false) UUID debugUserId,
      @RequestHeader(name = "X-Request-Id", required = false) String requestId) {

    if (debugUserId == null) {
      return ResponseEntity.status(HttpStatus.UNAUTHORIZED).build();
    }

    if (ifMatch == null) {
      return ResponseEntityUtils.preconditionRequired(CompliancePolicyResponse.class);
    }

    Long ifMatchVersion = ValidationUtils.parseIfMatch(ifMatch);

    CompliancePolicyResponse policy = policyService.update(tenantId, policyId, request,
        ifMatchVersion, debugUserId, requestId);
    return ResponseEntityUtils.ok(policy);
  }
}
