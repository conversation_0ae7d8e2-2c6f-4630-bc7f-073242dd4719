package io.securinest.controlplaneapi.controller.policy;

import io.securinest.controlplaneapi.dto.policy.SdkBundleResponse;
import io.securinest.controlplaneapi.service.policy.SdkBundleService;
import io.securinest.controlplaneapi.util.shared.ResponseEntityUtils;
import java.time.Duration;
import java.util.Optional;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.springframework.http.CacheControl;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
@RequestMapping("/sdk/bundles")
public class SdkBundlesController {

  private final SdkBundleService sdkBundleService;

  @GetMapping("/current")
  public ResponseEntity<SdkBundleResponse> getCurrentBundle(
      @RequestHeader(value = "If-None-Match", required = false) String ifNoneMatch,
      @RequestHeader(value = "X-Env-Key", required = false) String envKey,
      @RequestHeader(value = "X-Debug-TenantId", required = false) String debugTenantId,
      @RequestHeader(value = "X-Debug-ServiceId", required = false) String debugServiceId,
      @RequestHeader(value = "X-Debug-EnvId", required = false) String debugEnvId) {

    // TODO: Replace with proper API key authentication
    // For now, use debug headers for development
    if (debugTenantId == null || debugServiceId == null || debugEnvId == null) {
      return ResponseEntity.status(HttpStatus.UNAUTHORIZED).build();
    }

    UUID tenantId = UUID.fromString(debugTenantId);
    UUID serviceId = UUID.fromString(debugServiceId);
    UUID envId = UUID.fromString(debugEnvId);

    Optional<SdkBundleResponse> bundleOpt = sdkBundleService.getCurrent(tenantId, serviceId, envId,
        ifNoneMatch);

    if (bundleOpt.isEmpty()) {
      // Check if it's because of ETag match (304) or no bundle exists (404)
      String currentETag = sdkBundleService.getCurrentETag(tenantId, serviceId, envId);
      if (currentETag != null && currentETag.equals(ifNoneMatch)) {
        // 304 Not Modified
        return ResponseEntityUtils.notModified(currentETag, "private, max-age=60");
      } else {
        // 404 Not Found
        return ResponseEntity.notFound().build();
      }
    }

    SdkBundleResponse bundle = bundleOpt.get();

    return ResponseEntity.ok()
        .cacheControl(CacheControl.maxAge(Duration.ofSeconds(60)).cachePrivate())
        .eTag(bundle.etag())
        .body(bundle);
  }
}
