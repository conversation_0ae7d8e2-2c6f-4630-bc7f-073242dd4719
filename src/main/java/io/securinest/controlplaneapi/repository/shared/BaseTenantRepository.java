package io.securinest.controlplaneapi.repository.shared;

import java.util.Optional;
import java.util.UUID;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.repository.NoRepositoryBean;

@NoRepositoryBean
public interface BaseTenantRepository<T, ID> extends JpaRepository<T, ID> {

  Optional<T> findByIdAndTenantId(ID id, UUID tenantId);

  boolean existsByIdAndTenantId(ID id, UUID tenantId);

  Page<T> findAllByTenantId(UUID tenantId, Pageable pageable);

}
