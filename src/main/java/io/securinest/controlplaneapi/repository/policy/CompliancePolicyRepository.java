package io.securinest.controlplaneapi.repository.policy;

import io.securinest.controlplaneapi.entity.policy.CompliancePolicy;
import io.securinest.controlplaneapi.entity.shared.PolicyState;
import io.securinest.controlplaneapi.repository.shared.BaseTenantRepository;
import java.util.UUID;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

public interface CompliancePolicyRepository extends BaseTenantRepository<CompliancePolicy, UUID> {

  Page<CompliancePolicy> findAllByTenantIdAndState(UUID tenantId, PolicyState state,
      Pageable pageable);

  Page<CompliancePolicy> findAllByTenantIdOrderByUpdatedAtDesc(UUID tenantId, Pageable pageable);

  boolean existsByTenantIdAndNameIgnoreCase(UUID tenantId, String name);

  boolean existsByTenantIdAndNameIgnoreCaseAndIdNot(UUID tenantId, String name, UUID id);

}
