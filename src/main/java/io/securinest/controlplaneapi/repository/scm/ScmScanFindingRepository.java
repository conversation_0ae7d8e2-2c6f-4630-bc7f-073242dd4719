package io.securinest.controlplaneapi.repository.scm;

import io.securinest.controlplaneapi.entity.scm.ScmScanFinding;
import io.securinest.controlplaneapi.entity.shared.FindingSeverity;
import io.securinest.controlplaneapi.entity.shared.ScmFindingType;
import io.securinest.controlplaneapi.repository.shared.BaseTenantRepository;
import java.util.UUID;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

public interface ScmScanFindingRepository extends BaseTenantRepository<ScmScanFinding, UUID> {

  Page<ScmScanFinding> findAllByTenantIdAndTypeAndSeverity(UUID tenantId, ScmFindingType type,
      FindingSeverity severity, Pageable pageable);

  Page<ScmScanFinding> findAllByScanJobId(UUID scanJobId, Pageable pageable);

}
