package io.securinest.controlplaneapi.repository.scm;

import io.securinest.controlplaneapi.entity.scm.ScmRepo;
import io.securinest.controlplaneapi.repository.shared.BaseTenantRepository;
import java.util.Optional;
import java.util.UUID;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

public interface ScmRepoRepository extends BaseTenantRepository<ScmRepo, UUID> {

  Optional<ScmRepo> findByScmAccountIdAndExternalRepoId(UUID scmAccountId, String externalRepoId);

  Page<ScmRepo> findAllByTenantIdAndSelectedTrue(UUID tenantId, Pageable pageable);

  Page<ScmRepo> findAllByScmAccountId(UUID scmAccountId, Pageable pageable);

}
