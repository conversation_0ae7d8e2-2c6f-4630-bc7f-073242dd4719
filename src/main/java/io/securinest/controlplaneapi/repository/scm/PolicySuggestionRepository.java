package io.securinest.controlplaneapi.repository.scm;

import io.securinest.controlplaneapi.entity.scm.PolicySuggestion;
import io.securinest.controlplaneapi.repository.shared.BaseTenantRepository;
import java.util.UUID;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

public interface PolicySuggestionRepository extends BaseTenantRepository<PolicySuggestion, UUID> {

  Page<PolicySuggestion> findAllByTenantIdOrderByCreatedAtDesc(UUID tenantId, Pageable pageable);

}
