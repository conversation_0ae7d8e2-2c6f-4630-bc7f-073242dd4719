package io.securinest.controlplaneapi.repository.identity;

import io.securinest.controlplaneapi.entity.identity.UserAccount;
import java.util.Optional;
import java.util.UUID;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;

public interface UserAccountRepository extends JpaRepository<UserAccount, UUID> {

  Optional<UserAccount> findByKcSub(String kcSub);

  Optional<UserAccount> findByEmailIgnoreCase(String email);

  Page<UserAccount> findByEmailContainingIgnoreCase(String q, Pageable pageable);

  boolean existsByKcSub(String kcSub);

}
