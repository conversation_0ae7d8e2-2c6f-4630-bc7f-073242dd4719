package io.securinest.controlplaneapi.repository.inventory;

import io.securinest.controlplaneapi.entity.inventory.ObservedModel;
import io.securinest.controlplaneapi.repository.shared.BaseTenantRepository;
import java.util.Optional;
import java.util.UUID;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;

public interface ObservedModelRepository extends BaseTenantRepository<ObservedModel, UUID> {

  Page<ObservedModel> findAllByTenantIdOrderByLastSeenDesc(UUID tenantId, Pageable pageable);

  @Query("""
      select m from ObservedModel m
      where m.tenantId = :tenantId
      and m.provider = :provider
      and m.model = :model
      and coalesce(m.modelVersion,'') = coalesce(:version,'')
      and coalesce(m.region,'') = coalesce(:region,'')""")
  Optional<ObservedModel> findExact(UUID tenantId, String provider, String model, String version,
      String region);

}
