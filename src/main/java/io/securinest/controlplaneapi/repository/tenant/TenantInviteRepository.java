package io.securinest.controlplaneapi.repository.tenant;

import io.securinest.controlplaneapi.entity.tenant.TenantInvite;
import io.securinest.controlplaneapi.repository.shared.BaseTenantRepository;
import java.time.Instant;
import java.util.Optional;
import java.util.UUID;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

public interface TenantInviteRepository extends BaseTenantRepository<TenantInvite, UUID> {

  Optional<TenantInvite> findByTenantIdAndTokenHashAndAcceptedAtIsNullAndRevokedAtIsNull(
      UUID tenantId, String tokenHash);

  Page<TenantInvite> findAllByTenantIdAndExpiresAtAfter(UUID tenantId, Instant now,
      Pageable pageable);

  @Query("""
      update TenantInvite i
      set i.revokedAt = :ts
      where i.id = :id
      and i.tenantId = :tenantId
      and i.acceptedAt is null
      and i.revokedAt is null""")
  @Modifying
  int revoke(UUID tenantId, UUID id, Instant ts);
}
