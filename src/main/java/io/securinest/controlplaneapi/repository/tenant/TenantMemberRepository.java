package io.securinest.controlplaneapi.repository.tenant;

import io.securinest.controlplaneapi.entity.shared.Role;
import io.securinest.controlplaneapi.entity.tenant.TenantMember;
import io.securinest.controlplaneapi.entity.tenant.TenantMemberId;
import java.util.Optional;
import java.util.UUID;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;

public interface TenantMemberRepository extends JpaRepository<TenantMember, TenantMemberId> {

  Page<TenantMember> findAllByTenantId(UUID tenantId, Pageable pageable);

  Optional<TenantMember> findByTenantIdAndUserId(UUID tenantId, UUID userId);

  long countByTenantIdAndRole(UUID tenantId, Role role);

  boolean existsByTenantIdAndUserIdAndRole(UUID tenantId, UUID userId, Role role);

  long countByUserIdAndRole(UUID userId, Role role);

  Page<TenantMember> findAllByUserId(UUID userId, Pageable pageable);

  boolean existsByTenantIdAndUserId(UUID tenantId, UUID userId);
}
