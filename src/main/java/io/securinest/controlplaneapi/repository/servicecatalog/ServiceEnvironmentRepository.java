package io.securinest.controlplaneapi.repository.servicecatalog;

import io.securinest.controlplaneapi.entity.servicecatalog.ServiceEnvironment;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;

public interface ServiceEnvironmentRepository extends JpaRepository<ServiceEnvironment, UUID> {

  Optional<ServiceEnvironment> findByServiceIdAndEnvId(UUID serviceId, UUID envId);

  boolean existsByServiceIdAndEnvId(UUID serviceId, UUID envId);

  List<ServiceEnvironment> findAllByServiceId(UUID serviceId);

}
