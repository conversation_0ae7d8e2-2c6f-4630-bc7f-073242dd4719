package io.securinest.controlplaneapi.repository.servicecatalog;

import io.securinest.controlplaneapi.entity.servicecatalog.AppService;
import io.securinest.controlplaneapi.repository.shared.BaseTenantRepository;
import java.util.Optional;
import java.util.UUID;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

public interface AppServiceRepository extends BaseTenantRepository<AppService, UUID> {

  Optional<AppService> findByTenantIdAndSlug(UUID tenantId, String slug);

  boolean existsByTenantIdAndSlug(UUID tenantId, String slug);

  boolean existsByTenantIdAndNameIgnoreCase(UUID tenantId, String name);

  Page<AppService> findAllByTenantIdOrderByCreatedAtDesc(UUID tenantId, Pageable pageable);

}
