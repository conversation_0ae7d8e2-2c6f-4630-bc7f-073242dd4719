package io.securinest.controlplaneapi.repository.servicecatalog;

import io.securinest.controlplaneapi.entity.servicecatalog.Environment;
import io.securinest.controlplaneapi.repository.shared.BaseTenantRepository;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

public interface EnvironmentRepository extends BaseTenantRepository<Environment, UUID> {

  Optional<Environment> findByTenantIdAndKey(UUID tenantId, String key);

  boolean existsByTenantIdAndKey(UUID tenantId, String key);

  List<Environment> findAllByTenantId(UUID tenantId);

}
