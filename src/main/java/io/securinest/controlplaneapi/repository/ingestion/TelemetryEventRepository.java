package io.securinest.controlplaneapi.repository.ingestion;

import io.securinest.controlplaneapi.entity.ingestion.TelemetryEvent;
import io.securinest.controlplaneapi.entity.shared.EventType;
import java.time.Instant;
import java.util.UUID;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

public interface TelemetryEventRepository extends JpaRepository<TelemetryEvent, Long> {

  Page<TelemetryEvent> findAllByTenantIdAndTsBetweenOrderByTsDesc(UUID tenantId, Instant from,
      Instant to, Pageable pageable);

  Page<TelemetryEvent> findAllByTenantIdAndTypeAndTsBetweenOrderByTsDesc(UUID tenantId,
      EventType type, Instant from, Instant to, Pageable pageable);

  @Query("""
      select count(e)
      from TelemetryEvent e
      where e.tenantId = :tenantId
      and e.ts between :from and :to""")
  long countInRange(UUID tenantId, Instant from, Instant to);

}
