package io.securinest.controlplaneapi.entity.audit;

import io.securinest.controlplaneapi.entity.shared.AbstractTenantEvent;
import io.securinest.controlplaneapi.entity.shared.AuditAction;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.time.Instant;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "audit_log_entry")
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class AuditLogEntry extends AbstractTenantEvent {

  @Column(name = "actor_user_id")
  private UUID actorUserId;

  @NotNull
  @Enumerated(EnumType.STRING)
  @Column(name = "action", nullable = false, length = 120)
  private AuditAction action;

  @NotNull
  @NotBlank
  @Size(max = 120)
  @Column(name = "target_type", nullable = false, length = 120)
  private String targetType;

  @Column(name = "target_id")
  private UUID targetId;

  @NotNull
  @Column(name = "ts", nullable = false)
  private Instant ts;

  @Column(name = "diff", columnDefinition = "text")
  private String diff;

}
