package io.securinest.controlplaneapi.entity.tenant;

import io.securinest.controlplaneapi.entity.shared.AbstractEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "tenant")
@NoArgsConstructor
@AllArgsConstructor
@Setter
@Getter
public class Tenant extends AbstractEntity {

  @NotBlank
  @Size(min = 3, max = 200)
  @Column(name = "name", nullable = false, length = 200)
  private String name;

  @NotBlank
  @Pattern(regexp = "^[a-z0-9-]{3,120}$")
  @Column(name = "slug", nullable = false, length = 120)
  private String slug;

  @NotBlank
  @Column(name = "billing_plan", nullable = false)
  private String billingPlan = "free";

  @NotBlank
  @Column(name = "region", nullable = false)
  private String region;

}
