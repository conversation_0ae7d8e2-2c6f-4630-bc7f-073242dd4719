package io.securinest.controlplaneapi.entity.tenant;

import io.securinest.controlplaneapi.entity.shared.AbstractTenantEntity;
import io.securinest.controlplaneapi.entity.shared.Role;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.PrePersist;
import jakarta.persistence.PreUpdate;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.time.Instant;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "tenant_invite")
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class TenantInvite extends AbstractTenantEntity {

  @NotBlank
  @Email
  @Column(name = "email", nullable = false)
  private String email;

  @NotNull
  @Enumerated(EnumType.STRING)
  @Column(name = "role", nullable = false)
  private Role role;

  @NotBlank
  @Column(name = "token_hash", nullable = false, unique = true)
  private String tokenHash;

  @NotNull
  @Column(name = "expires_at", nullable = false)
  private Instant expiresAt;

  @Column(name = "accepted_at")
  private Instant acceptedAt;

  @Column(name = "revoked_at")
  private Instant revokedAt;

  @PrePersist
  @PreUpdate
  void normalize() {
    if (email != null) {
      email = email.trim().toLowerCase();
    }
  }

}
