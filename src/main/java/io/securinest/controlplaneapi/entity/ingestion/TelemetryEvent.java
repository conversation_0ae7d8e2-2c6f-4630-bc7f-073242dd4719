package io.securinest.controlplaneapi.entity.ingestion;

import io.securinest.controlplaneapi.entity.shared.AbstractTenantEvent;
import io.securinest.controlplaneapi.entity.shared.EventType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Size;
import java.time.Instant;
import java.util.Map;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

@Entity
@Table(name = "telemetry_event")
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class TelemetryEvent extends AbstractTenantEvent {

  @Column(name = "service_id", nullable = false)
  private UUID serviceId;

  @Column(name = "env_id", nullable = false)
  private UUID envId;

  @Column(name = "ts", nullable = false)
  private Instant ts;

  @Enumerated(EnumType.STRING)
  @Column(name = "type", nullable = false)
  private EventType type;

  @Size(max = 400)
  @Column(name = "endpoint", length = 400)
  private String endpoint;

  @Size(max = 60)
  @Column(name = "policy_ver", length = 60)
  private String policyVer;

  @Size(max = 100)
  @Column(name = "policy_hash", length = 100)
  private String policyHash;

  @Size(max = 80)
  @Column(name = "violation", length = 80)
  private String violation;

  @Min(0)
  @Column(name = "latency_ms")
  private Integer latencyMs;

  @JdbcTypeCode(SqlTypes.JSON)
  @Column(name = "payload", columnDefinition = "jsonb")
  private Map<String, Object> payload;

}
