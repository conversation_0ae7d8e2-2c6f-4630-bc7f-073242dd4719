package io.securinest.controlplaneapi.entity.scm;

import io.securinest.controlplaneapi.entity.shared.AbstractTenantEntity;
import io.securinest.controlplaneapi.entity.shared.FindingSeverity;
import io.securinest.controlplaneapi.entity.shared.ScmFindingType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.util.Map;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

@Entity
@Table(name = "scm_scan_finding")
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class ScmScanFinding extends AbstractTenantEntity {

  @NotNull
  @Column(name = "scan_job_id", nullable = false)
  private UUID scanJobId;

  @NotNull
  @Column(name = "repo_id", nullable = false)
  private UUID repoId;

  @NotNull
  @Enumerated(EnumType.STRING)
  @Column(name = "type", nullable = false)
  private ScmFindingType type;

  @Size(max = 255)
  @Column(name = "rule_id")
  private String ruleId;

  @NotNull
  @Enumerated(EnumType.STRING)
  @Column(name = "severity", nullable = false)
  private FindingSeverity severity;

  @NotNull
  @Column(name = "path", nullable = false)
  private String path;

  @Min(1)
  @Column(name = "line")
  private Integer line;

  @Column(name = "snippet_hash")
  private String snippetHash;

  @JdbcTypeCode(SqlTypes.JSON)
  @Column(name = "details", columnDefinition = "jsonb")
  private Map<String, Object> details;

  @Size(max = 64)
  @Column(name = "commit_sha", length = 64)
  private String commitSha;

}
