package io.securinest.controlplaneapi.entity.scm;

import io.securinest.controlplaneapi.entity.shared.AbstractTenantEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.time.Instant;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "scm_repo")
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class ScmRepo extends AbstractTenantEntity {

  @NotNull
  @Column(name = "scm_account_id", nullable = false)
  private UUID scmAccountId;

  @NotBlank
  @Size(max = 120)
  @Column(name = "external_repo_id", nullable = false, length = 120)
  private String externalRepoId;

  @NotBlank
  @Column(name = "name", nullable = false)
  private String name;

  @Column(name = "default_branch")
  private String defaultBranch;

  @Column(name = "visibility")
  private String visibility;

  @NotNull
  @Column(name = "selected", nullable = false)
  private boolean selected = false;

  @Column(name = "last_synced_at")
  private Instant lastSyncedAt;

}
