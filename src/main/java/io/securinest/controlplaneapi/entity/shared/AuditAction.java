package io.securinest.controlplaneapi.entity.shared;

public enum AuditAction {

  // Identity & Access
  USER_LINKED_IDP,
  MEMBER_ADDED,
  MEMBER_ROLE_CHANGED,
  MEMBER_REMOVED,

  // Invites & domains
  INVITE_CREATED,
  INVITE_REVOKED,
  INVITE_ACCEPTED,
  TENANT_DOMAIN_ADDED,
  TENANT_DOMAIN_VERIFIED,
  TENANT_DOMAIN_REMOVED,

  // Tenant lifecycle
  TENANT_CREATED,
  TENANT_UPDATED,
  TRIAL_TENANT_CREATED,
  TRIAL_TENANT_PURGED,

  // Environments, Services, API keys
  ENV_CREATED,
  ENV_RENAMED,
  ENV_DELETED,

  SERVICE_CREATED,
  SERVICE_UPDATED,
  SERVICE_DELETED,

  SERVICE_ENV_LINKED,
  SERVICE_ENV_UNLINKED,

  SERVICE_API_KEY_CREATED,
  SERVICE_API_KEY_ROTATED,
  SERVICE_API_KEY_REVOKED,
  SERVICE_API_KEY_RENAMED,

  SERVICE_ENFORCEMENT_MODE_CHANGED,

  // Policies & Bundles
  POLICY_CREATED,
  POLICY_UPDATED,
  POLICY_VERSION_CREATED,
  POLICY_VERSION_DELETED,

  POLICY_VERSION_APPROVED,
  POLICY_VERSION_ACTIVATED,
  POLICY_VERSION_ARCHIVED,

  POLICY_BUNDLE_COMPILED,
  POLICY_BUNDLE_SIGNED,
  POLICY_BUNDLE_ACTIVATED,
  POLICY_BUNDLE_SUPERSEDED,
  POLICY_BUNDLE_ROLLED_BACK,

  SIGNING_KEY_ADDED,
  SIGNING_KEY_REVOKED,
  SIGNING_KEY_ROTATED,

  // Policy suggestions
  POLICY_SUGGESTION_CREATED,
  POLICY_SUGGESTION_APPLIED,

  // Inventory
  OBSERVED_ENDPOINT_UPSERTED,
  OBSERVED_MODEL_UPSERTED,

  // Findings
  FINDING_OPENED,
  FINDING_UPDATED,
  FINDING_ACKNOWLEDGED,
  FINDING_SUPPRESSED,
  FINDING_RESOLVED,
  FINDING_REOPENED,

  // Usage
  USAGE_DAILY_ROLLUP_WRITTEN,

  // SCM Connect & Scan
  SCM_ACCOUNT_CONNECTED,
  SCM_ACCOUNT_DISCONNECTED,
  SCM_REPOS_SELECTED_UPDATED,

  SCM_SCAN_QUEUED,
  SCM_SCAN_STARTED,
  SCM_SCAN_COMPLETED,
  SCM_SCAN_FAILED,

  SCM_FINDING_CREATED,
  SCM_FINDING_SUPPRESSED,
  SCM_FINDING_RESOLVED,

  // Webhooks
  WEBHOOK_ENDPOINT_CREATED,
  WEBHOOK_ENDPOINT_UPDATED,
  WEBHOOK_ENDPOINT_DELETED,
  WEBHOOK_DELIVERY_FAILED,
  WEBHOOK_DELIVERY_RETRIED,
  WEBHOOK_DELIVERY_SUCCEEDED,

  // Audit Packs
  AUDIT_PACK_REQUESTED,
  AUDIT_PACK_QUEUED,
  AUDIT_PACK_GENERATED,
  AUDIT_PACK_SUCCEEDED,
  AUDIT_PACK_FAILED,
  AUDIT_PACK_SIGNED,
  AUDIT_PACK_DOWNLOAD_LINK_ISSUED,
  AUDIT_PACK_DELETED

}
