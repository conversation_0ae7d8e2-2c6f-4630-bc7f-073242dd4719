package io.securinest.controlplaneapi.entity.inventory;

import io.securinest.controlplaneapi.entity.shared.AbstractTenantEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.time.Instant;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "observed_model")
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class ObservedModel extends AbstractTenantEntity {

  @NotBlank
  @Size(max = 80)
  @Column(name = "provider", nullable = false, length = 80)
  private String provider;

  @NotBlank
  @Size(max = 120)
  @Column(name = "model", nullable = false, length = 120)
  private String model;

  @Size(max = 80)
  @Column(name = "model_version", length = 80)
  private String modelVersion;

  @Size(max = 40)
  @Column(name = "region", length = 40)
  private String region;

  @NotNull
  @Column(name = "first_seen", nullable = false)
  private Instant firstSeen;

  @NotNull
  @Column(name = "last_seen", nullable = false)
  private Instant lastSeen;

}
