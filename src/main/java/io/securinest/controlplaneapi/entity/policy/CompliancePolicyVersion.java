package io.securinest.controlplaneapi.entity.policy;

import io.securinest.controlplaneapi.entity.shared.AbstractEntity;
import io.securinest.controlplaneapi.entity.shared.PolicyState;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

@Entity
@Table(name = "compliance_policy_version")
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class CompliancePolicyVersion extends AbstractEntity {

  @NotNull
  @Column(name = "policy_id", nullable = false)
  private UUID policyId;

  @NotNull
  @Column(name = "version_no", nullable = false)
  private Integer versionNo;

  @Column(name = "author_id")
  private UUID authorId;

  @NotNull
  @JdbcTypeCode(SqlTypes.JSON)
  @Column(name = "json", nullable = false, columnDefinition = "jsonb")
  private String json;

  @NotNull
  @Enumerated(EnumType.STRING)
  @Column(name = "status", nullable = false)
  private PolicyState status = PolicyState.DRAFT;

}
