package io.securinest.controlplaneapi.entity.policy;

import io.securinest.controlplaneapi.entity.shared.AbstractTenantEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import java.time.Instant;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "signing_key")
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class SigningKey extends AbstractTenantEntity {

  @NotBlank
  @Size(max = 64)
  @Column(name = "kid", nullable = false, length = 64, unique = true)
  private String kid;

  @NotBlank
  @Column(name = "public_key_pem", nullable = false)
  private String publicKeyPem;

  @Column(name = "revoked_at")
  private Instant revokedAt;

}
