package io.securinest.controlplaneapi.entity.identity;

import io.securinest.controlplaneapi.entity.shared.AbstractEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.PrePersist;
import jakarta.persistence.PreUpdate;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

@Entity
@Table(name = "user_account")
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class UserAccount extends AbstractEntity {

  @NotNull
  @NotBlank
  @Size(max = 64)
  @Column(name = "kc_sub", nullable = false, length = 64, unique = true)
  private String kcSub;

  @NotNull
  @NotBlank
  @Email
  @Column(name = "email", nullable = false)
  private String email;

  @Size(max = 200)
  @Column(name = "display_name", length = 200)
  private String displayName;

  @PrePersist
  @PreUpdate
  void normalize() {
    if (StringUtils.isNotEmpty(email)) {
      email = email.trim().toLowerCase();
    }
  }

}
