package io.securinest.controlplaneapi.entity.servicecatalog;

import io.securinest.controlplaneapi.entity.shared.AbstractTenantEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "app_service")
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class AppService extends AbstractTenantEntity {

  @NotBlank
  @Size(min = 3, max = 200)
  @Column(name = "name", nullable = false, length = 200)
  private String name;

  @NotBlank
  @Pattern(regexp = "^[a-z0-9-]{3,120}$")
  @Column(name = "slug", nullable = false, length = 120)
  private String slug;

  @Size(max = 1000)
  @Column(name = "description", length = 1000)
  private String description;

}
