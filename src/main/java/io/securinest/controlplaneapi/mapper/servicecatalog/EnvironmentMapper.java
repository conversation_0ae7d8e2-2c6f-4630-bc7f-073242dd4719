package io.securinest.controlplaneapi.mapper.servicecatalog;

import io.securinest.controlplaneapi.dto.servicecatalog.EnvironmentResponse;
import io.securinest.controlplaneapi.entity.servicecatalog.Environment;
import org.mapstruct.InjectionStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

@Mapper(
    componentModel = "spring",
    injectionStrategy = InjectionStrategy.CONSTRUCTOR,
    unmappedTargetPolicy = ReportingPolicy.IGNORE
)
public interface EnvironmentMapper {

  EnvironmentResponse mapToEnvironmentResponse(Environment environment);

}
