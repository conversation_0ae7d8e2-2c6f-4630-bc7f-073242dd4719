package io.securinest.controlplaneapi.mapper.identity;

import io.securinest.controlplaneapi.dto.identity.UserAccountResponse;
import io.securinest.controlplaneapi.entity.identity.UserAccount;
import org.mapstruct.InjectionStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

@Mapper(
    componentModel = "spring",
    injectionStrategy = InjectionStrategy.CONSTRUCTOR,
    unmappedTargetPolicy = ReportingPolicy.IGNORE
)
public interface UserAccountMapper {

  @Mapping(target = "id", expression = "java(u.getId() != null ? u.getId().toString() : null)")
  @Mapping(target = "version", expression = "java(u.getVersion())")
  UserAccountResponse toResponse(UserAccount u);
}
