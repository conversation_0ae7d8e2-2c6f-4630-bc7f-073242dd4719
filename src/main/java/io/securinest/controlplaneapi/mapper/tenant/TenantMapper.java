package io.securinest.controlplaneapi.mapper.tenant;

import io.securinest.controlplaneapi.dto.tenant.TenantResponse;
import io.securinest.controlplaneapi.entity.tenant.Tenant;
import org.mapstruct.InjectionStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

@Mapper(
    componentModel = "spring",
    injectionStrategy = InjectionStrategy.CONSTRUCTOR,
    unmappedTargetPolicy = ReportingPolicy.IGNORE
)
public interface TenantMapper {

  @Mapping(target = "id", expression = "java(t.getId() != null ? t.getId().toString() : null)")
  @Mapping(target = "version", expression = "java(t.getVersion())")
  TenantResponse toResponse(Tenant t);
}

