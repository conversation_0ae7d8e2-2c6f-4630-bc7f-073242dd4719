package io.securinest.controlplaneapi.util.shared;

import io.securinest.controlplaneapi.dto.shared.VersionedResource;
import java.net.URI;
import java.security.MessageDigest;
import lombok.experimental.UtilityClass;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;

@UtilityClass
public class ResponseEntityUtils {

  public static <T extends VersionedResource> ResponseEntity<T> updateResponseWithVersionedResource(
      T res) {
    return ResponseEntity.ok()
        .header(HttpHeaders.CACHE_CONTROL, Constants.CACHE_NO_STORE)
        .eTag(strongETagFromVersion(res.version()))
        .body(res);
  }

  public static <T> ResponseEntity<T> updateResponseWithVersionedResource(T res, Long version) {
    return ResponseEntity.ok()
        .header(HttpHeaders.CACHE_CONTROL, Constants.CACHE_NO_STORE)
        .eTag(strongETagFromVersion(version))
        .body(res);
  }

  public static <T extends VersionedResource> ResponseEntity<T> createResponseWithVersionedResource(
      T res) {
    URI location = ServletUriComponentsBuilder.fromCurrentRequest()
        .path("/{id}")
        .buildAndExpand(res.id())
        .toUri();

    return ResponseEntity.created(location)
        .header(HttpHeaders.CACHE_CONTROL, Constants.CACHE_NO_STORE)
        .eTag(strongETagFromVersion(res.version()))
        .body(res);
  }

  public static <T> ResponseEntity<T> createResponseWithVersionedResource(T res, String id,
      String path, Long version) {
    URI location = ServletUriComponentsBuilder.fromCurrentRequest()
        .path(path)
        .buildAndExpand(id)
        .toUri();

    return ResponseEntity.created(location)
        .header(HttpHeaders.CACHE_CONTROL, Constants.CACHE_NO_STORE)
        .eTag(strongETagFromVersion(version))
        .body(res);
  }

  public static <T extends VersionedResource> ResponseEntity<T> getResponseWithVersionedResource(
      T res) {
    return ResponseEntity.ok()
        .header(HttpHeaders.CACHE_CONTROL, Constants.DEFAULT_CACHE_CONTROL)
        .eTag(strongETagFromVersion(res.version()))
        .body(res);
  }

  public static <T> ResponseEntity<T> getResponseWithVersionedResource(T res, Long version) {
    return ResponseEntity.ok()
        .header(HttpHeaders.CACHE_CONTROL, Constants.CACHE_NO_STORE)
        .eTag(strongETagFromVersion(version))
        .body(res);
  }

  public static ResponseEntity<Void> deleteNoContent() {
    return ResponseEntity.noContent()
        .header(HttpHeaders.CACHE_CONTROL, Constants.CACHE_NO_STORE)
        .build();
  }

  public static <T> ResponseEntity<T> ok(T body) {
    return ResponseEntity.ok()
        .header(HttpHeaders.CACHE_CONTROL, Constants.CACHE_NO_STORE)
        .body(body);
  }

  public static <T> ResponseEntity<T> created(T body) {
    return ResponseEntity.status(201)
        .header(HttpHeaders.CACHE_CONTROL, Constants.CACHE_NO_STORE)
        .body(body);
  }

  public static <T> ResponseEntity<T> preconditionRequired(Class<T> responseType) {
    return ResponseEntity.status(HttpStatus.PRECONDITION_REQUIRED).build();
  }

  public static <T> ResponseEntity<T> preconditionFailed(Class<T> responseType) {
    return ResponseEntity.status(HttpStatus.PRECONDITION_FAILED).build();
  }

  public static <T> ResponseEntity<T> notModified(Class<T> responseType) {
    return ResponseEntity.status(HttpStatus.NOT_MODIFIED).build();
  }

  public static <T> ResponseEntity<T> statusWithHeaders(HttpStatus status, String eTag,
      String cacheControl) {
    ResponseEntity.BodyBuilder builder = ResponseEntity.status(status);

    if (eTag != null) {
      builder.eTag(eTag);
    }

    if (cacheControl != null) {
      builder.header(HttpHeaders.CACHE_CONTROL, cacheControl);
    }

    return builder.build();
  }

  public static <T> ResponseEntity<T> notModified(String eTag, String cacheControl) {
    return statusWithHeaders(HttpStatus.NOT_MODIFIED, eTag, cacheControl);
  }

  public static <T> ResponseEntity<T> notModified(String eTag) {
    return notModified(eTag, Constants.DEFAULT_CACHE_CONTROL);
  }


  public static String strongETagFromVersion(long version) {
    if (version < 0) {
      throw new IllegalArgumentException("version must be >= 0");
    }
    return "\"" + version + "\"";
  }

  // will be used in GET /sdk/bundles/current
  public static String strongETagFromSha256(byte[] canonicalBytes) {
    return "\"" + sha256Hex(canonicalBytes) + "\"";
  }

  private static String sha256Hex(byte[] bytes) {
    try {
      MessageDigest digest = MessageDigest.getInstance("SHA-256");
      byte[] hash = digest.digest(bytes);
      StringBuilder hexString = new StringBuilder();
      for (byte b : hash) {
        String hex = Integer.toHexString(0xff & b);
        if (hex.length() == 1) {
          hexString.append('0');
        }
        hexString.append(hex);
      }
      return hexString.toString();
    } catch (java.security.NoSuchAlgorithmException e) {
      throw new RuntimeException("SHA-256 not available", e);
    }
  }
}

