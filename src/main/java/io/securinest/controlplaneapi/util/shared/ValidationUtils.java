package io.securinest.controlplaneapi.util.shared;

import io.securinest.controlplaneapi.exceptions.shared.SecurinestException;
import java.util.Locale;
import java.util.UUID;
import lombok.experimental.UtilityClass;
import org.springframework.http.HttpStatus;

@UtilityClass
public class ValidationUtils {


  public static Long parseIfMatch(String ifMatch) {
    if (ifMatch == null) {
      return null;
    }
    for (String token : ifMatch.split(",")) {
      String value = token.trim();
      if (value.startsWith("W/")) {
        value = value.substring(2).trim();
      }
      if (value.startsWith("\"") && value.endsWith("\"")) {
        value = value.substring(1, value.length() - 1);
      }
      if (value.startsWith("v")) {
        try {
          return Long.parseLong(value.substring(1));
        } catch (NumberFormatException ignored) {
        }
      }
      try {
        return Long.parseLong(value);
      } catch (NumberFormatException ignored) {
      }
    }
    return null;
  }

  public static void validateName(String nameValue, String fieldName) {
    if (nameValue == null || nameValue.length() < Constants.NAME_MIN_LENGTH
        || nameValue.length() > Constants.NAME_MAX_LENGTH) {
      throw new SecurinestException(HttpStatus.BAD_REQUEST,
          fieldName + " must be " + Constants.NAME_MIN_LENGTH + "-" + Constants.NAME_MAX_LENGTH
              + " characters");
    }
  }

  public static String normalizeSlug(String rawSlug) {
    String trimmedSlug = trimToNull(rawSlug);
    if (trimmedSlug == null) {
      return null;
    }
    return trimmedSlug.toLowerCase(Locale.ROOT)
        .replaceAll("[\\s_]+", "-")
        .replaceAll("^-+|-+$", "");
  }

  public static String normalizeName(String rawName) {
    return trimToNull(rawName);
  }

  public static String trimToNull(String inputValue) {
    if (inputValue == null) {
      return null;
    }
    String trimmedValue = inputValue.trim();
    return trimmedValue.isEmpty() ? null : trimmedValue;
  }


  public static long defaultLong(Long inputValue, long defaultValue) {
    return inputValue == null ? defaultValue : inputValue;
  }

  public static String normalizeEmail(String email) {
    if (email == null) {
      throw new SecurinestException(HttpStatus.BAD_REQUEST, "Email is required");
    }
    return email.trim().toLowerCase();
  }

  public static void validateEmail(String email) {
    if (email == null || email.isBlank()) {
      throw new SecurinestException(HttpStatus.BAD_REQUEST, "Email is required");
    }
    if (!Constants.EMAIL_PATTERN.matcher(email).matches()) {
      throw new SecurinestException(HttpStatus.BAD_REQUEST, "Invalid email format");
    }
  }

  public static UUID parseUuid(String value, String fieldName) {
    try {
      return UUID.fromString(value);
    } catch (Exception e) {
      throw new SecurinestException(HttpStatus.BAD_REQUEST, "Invalid UUID for " + fieldName);
    }
  }

}
