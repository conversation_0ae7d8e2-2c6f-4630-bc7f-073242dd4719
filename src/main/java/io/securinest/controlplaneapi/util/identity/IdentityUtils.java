package io.securinest.controlplaneapi.util.identity;

import io.securinest.controlplaneapi.entity.identity.UserAccount;
import lombok.experimental.UtilityClass;

@UtilityClass
public class IdentityUtils {

  public static UserAccount createUser(String normalizedEmail, String normalizedDisplayName) {
    UserAccount newUser = new UserAccount();
    newUser.setKcSub("temp");
    newUser.setEmail(normalizedEmail);
    newUser.setDisplayName(normalizedDisplayName);
    return newUser;
  }
}
