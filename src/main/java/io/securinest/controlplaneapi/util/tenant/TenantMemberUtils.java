package io.securinest.controlplaneapi.util.tenant;

import io.securinest.controlplaneapi.entity.shared.Role;
import io.securinest.controlplaneapi.exceptions.shared.SecurinestException;
import java.util.Locale;
import java.util.Objects;
import java.util.UUID;
import org.springframework.http.HttpStatus;

public final class TenantMemberUtils {

  public static boolean hasAtLeastViewer(Role role) {
    return role == Role.OWNER || role == Role.ADMIN || role == Role.POLICY_ADMIN
        || role == Role.AUDITOR || role == Role.VIEWER;
  }

  public static boolean isAdminOrOwner(Role role) {
    return role == Role.OWNER || role == Role.ADMIN;
  }


  public static Role parseRole(String raw) {
    if (raw == null || raw.isBlank()) {
      throw new SecurinestException(HttpStatus.BAD_REQUEST, "Role is required");
    }
    try {
      return Role.valueOf(raw.trim().toUpperCase(Locale.ROOT));
    } catch (IllegalArgumentException e) {
      throw new SecurinestException(HttpStatus.BAD_REQUEST, "Invalid role");
    }
  }

  public static String safeToString(UUID id) {
    return id != null ? id.toString() : null;
  }

  public static String maskEmail(String email) {
    int at = email.indexOf('@');
    if (at <= 1) {
      return "***" + email.substring(Math.max(at, 0));
    }
    String name = email.substring(0, at);
    String domain = email.substring(at);
    String visible = name.substring(0, 1);
    return visible + "***" + domain;
  }

  public static void validateOwnerRoleChange(
      Role currentRole,
      Role newRole,
      Role requesterRole,
      long ownerCount,
      UUID requesterId,
      UUID targetUserId
  ) {
    if (currentRole == Role.OWNER || newRole == Role.OWNER) {
      if (requesterRole != Role.OWNER) {
        throw new SecurinestException(HttpStatus.FORBIDDEN, "Only OWNER can manage OWNER role");
      }
    }

    if (currentRole == Role.OWNER && newRole != Role.OWNER) {
      if (ownerCount == 1 && Objects.equals(requesterId, targetUserId)) {
        throw new SecurinestException(HttpStatus.BAD_REQUEST,
            "Cannot self-demote as the last OWNER");
      }

      if (ownerCount <= 1) {
        throw new SecurinestException(HttpStatus.BAD_REQUEST, "Cannot demote the last OWNER");
      }
    }
  }

  public static void validateOwnerRemoval(
      Role memberRole,
      Role requesterRole,
      long ownerCount,
      UUID requesterId,
      UUID targetUserId
  ) {
    if (memberRole == Role.OWNER) {
      if (requesterRole != Role.OWNER) {
        throw new SecurinestException(HttpStatus.FORBIDDEN, "Only OWNER can remove an OWNER");
      }

      if (ownerCount == 1 && Objects.equals(requesterId, targetUserId)) {
        throw new SecurinestException(HttpStatus.BAD_REQUEST,
            "Cannot remove yourself as the last OWNER");
      }

      if (ownerCount <= 1) {
        throw new SecurinestException(HttpStatus.BAD_REQUEST, "Cannot remove the last OWNER");
      }
    }
  }
}

