package io.securinest.controlplaneapi.service.policy;

import io.securinest.controlplaneapi.dto.policy.CompliancePolicyVersionCreateRequest;
import io.securinest.controlplaneapi.dto.policy.CompliancePolicyVersionResponse;
import io.securinest.controlplaneapi.entity.policy.CompliancePolicyVersion;
import io.securinest.controlplaneapi.entity.shared.PolicyState;
import io.securinest.controlplaneapi.exceptions.policy.PolicyNotFoundException;
import io.securinest.controlplaneapi.exceptions.shared.SecurinestException;
import io.securinest.controlplaneapi.mapper.policy.CompliancePolicyVersionMapper;
import io.securinest.controlplaneapi.repository.policy.CompliancePolicyRepository;
import io.securinest.controlplaneapi.repository.policy.CompliancePolicyVersionRepository;
import java.time.Instant;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.server.ResponseStatusException;

@Service
@RequiredArgsConstructor
@Slf4j
public class CompliancePolicyVersionService {

  private final CompliancePolicyRepository policyRepository;
  private final CompliancePolicyVersionRepository versionRepository;
  private final CompliancePolicyVersionMapper versionMapper;
  private final PolicyJsonValidationService jsonValidationService;

  @Transactional
  public CompliancePolicyVersionResponse createVersion(UUID tenantId, UUID policyId,
      CompliancePolicyVersionCreateRequest request, UUID authorId, String requestId) {
    if (tenantId == null) {
      throw new SecurinestException(HttpStatus.BAD_REQUEST, "Missing tenant ID");
    }
    if (policyId == null) {
      throw new SecurinestException(HttpStatus.BAD_REQUEST, "Missing policy ID");
    }
    if (request == null) {
      throw new SecurinestException(HttpStatus.BAD_REQUEST, "Missing body");
    }
    if (authorId == null) {
      throw new SecurinestException(HttpStatus.UNAUTHORIZED, "Missing current user");
    }

    log.debug("Creating policy version for policy: {} in tenant: {}", policyId, tenantId);

    // Validate policy exists and belongs to tenant
    policyRepository.findByIdAndTenantId(policyId, tenantId)
        .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Policy not found"));

    // Validate JSON against schema and size constraints
    jsonValidationService.validatePolicyJson(request.json());

    // Compute next version number
    int nextVersionNo = versionRepository.maxVersion(policyId) + 1;

    CompliancePolicyVersion version = versionMapper.fromRequest(request);
    version.setPolicyId(policyId);
    version.setVersionNo(nextVersionNo);
    version.setAuthorId(authorId);
    version.setCreatedAt(Instant.now());

    try {
      CompliancePolicyVersion saved = versionRepository.save(version);
      log.info("Created policy version: {} (v{}) for policy: {}", saved.getId(), nextVersionNo,
          policyId);
      return versionMapper.toResponse(saved);
    } catch (DataIntegrityViolationException e) {
      log.warn("createVersion constraint issue. requestId={} msg={}", requestId, e.getMessage());
      throw new SecurinestException(HttpStatus.CONFLICT, "Conflict creating policy version");
    }
  }

  @Transactional(readOnly = true)
  public Page<CompliancePolicyVersionResponse> listVersions(UUID tenantId, UUID policyId,
      Pageable pageable) {
    log.debug("Listing policy versions for policy: {} in tenant: {}", policyId, tenantId);

    // Validate policy exists and belongs to tenant
    if (!policyRepository.existsByIdAndTenantId(policyId, tenantId)) {
      throw new PolicyNotFoundException("Policy not found: " + policyId);
    }

    Page<CompliancePolicyVersion> versions = versionRepository
        .findAllByPolicyIdOrderByVersionNoDesc(policyId, pageable);

    return versions.map(versionMapper::toResponse);
  }

  @Transactional(readOnly = true)
  public CompliancePolicyVersionResponse getVersion(UUID tenantId, UUID policyId, int versionNo,
      UUID requesterId) {
    if (tenantId == null) {
      throw new SecurinestException(HttpStatus.BAD_REQUEST, "Missing tenant ID");
    }
    if (policyId == null) {
      throw new SecurinestException(HttpStatus.BAD_REQUEST, "Missing policy ID");
    }
    if (requesterId == null) {
      throw new SecurinestException(HttpStatus.UNAUTHORIZED, "Missing current user");
    }

    log.debug("Getting policy version: {} v{} in tenant: {}", policyId, versionNo, tenantId);

    // Validate policy exists and belongs to tenant
    if (!policyRepository.existsByIdAndTenantId(policyId, tenantId)) {
      throw new ResponseStatusException(HttpStatus.NOT_FOUND, "Policy not found");
    }

    CompliancePolicyVersion version = versionRepository.findByPolicyIdAndVersionNo(policyId,
            versionNo)
        .orElseThrow(
            () -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Policy version not found"));

    return versionMapper.toResponse(version);
  }

  @Transactional
  public CompliancePolicyVersionResponse updateStatus(UUID tenantId, UUID policyId, int versionNo,
      PolicyState newStatus, UUID requesterId, String requestId) {
    if (tenantId == null) {
      throw new SecurinestException(HttpStatus.BAD_REQUEST, "Missing tenant ID");
    }
    if (policyId == null) {
      throw new SecurinestException(HttpStatus.BAD_REQUEST, "Missing policy ID");
    }
    if (newStatus == null) {
      throw new SecurinestException(HttpStatus.BAD_REQUEST, "Missing status");
    }
    if (requesterId == null) {
      throw new SecurinestException(HttpStatus.UNAUTHORIZED, "Missing current user");
    }

    log.debug("Updating status of policy version: {} v{} to {} in tenant: {}",
        policyId, versionNo, newStatus, tenantId);

    // Validate policy exists and belongs to tenant
    if (!policyRepository.existsByIdAndTenantId(policyId, tenantId)) {
      throw new ResponseStatusException(HttpStatus.NOT_FOUND, "Policy not found");
    }

    CompliancePolicyVersion version = versionRepository.findByPolicyIdAndVersionNo(policyId,
            versionNo)
        .orElseThrow(
            () -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Policy version not found"));

    // Check if status is already the same
    if (version.getStatus() == newStatus) {
      return versionMapper.toResponse(version);
    }

    // Validate state transition (P1: only DRAFT -> ACTIVE allowed)
    if (!(version.getStatus() == PolicyState.DRAFT && newStatus == PolicyState.ACTIVE)) {
      throw new SecurinestException(HttpStatus.BAD_REQUEST, "Invalid status transition from " +
          version.getStatus() + " to " + newStatus);
    }

    try {
      // If setting to ACTIVE, demote any existing ACTIVE version to DRAFT
      versionRepository.updateStatusByPolicyIdAndStatus(tenantId, policyId, PolicyState.ACTIVE,
          PolicyState.DRAFT);

      version.setStatus(newStatus);
      CompliancePolicyVersion saved = versionRepository.save(version);

      log.info("Updated policy version: {} v{} status to {} in tenant: {} by user: {}",
          policyId, versionNo, newStatus, tenantId, requesterId);

      return versionMapper.toResponse(saved);
    } catch (DataIntegrityViolationException e) {
      log.warn("updateStatus constraint issue. requestId={} msg={}", requestId, e.getMessage());
      throw new SecurinestException(HttpStatus.CONFLICT, "Conflict updating policy version status");
    }
  }
}
