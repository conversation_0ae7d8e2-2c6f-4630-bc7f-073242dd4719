package io.securinest.controlplaneapi.service;

import io.securinest.controlplaneapi.dto.audit.AuditLogEntryResponse;
import io.securinest.controlplaneapi.dto.audit.AuditPackRequest;
import io.securinest.controlplaneapi.dto.audit.AuditPackResponse;
import io.securinest.controlplaneapi.dto.shared.PageResponse;
import io.securinest.controlplaneapi.entity.audit.AuditLogEntry;
import io.securinest.controlplaneapi.entity.audit.AuditPack;
import io.securinest.controlplaneapi.entity.shared.AuditAction;
import io.securinest.controlplaneapi.entity.shared.JobStatus;
import io.securinest.controlplaneapi.entity.shared.Role;
import io.securinest.controlplaneapi.entity.tenant.TenantMember;
import io.securinest.controlplaneapi.exceptions.shared.SecurinestException;
import io.securinest.controlplaneapi.mapper.audit.AuditMapper;
import io.securinest.controlplaneapi.repository.audit.AuditLogEntryRepository;
import io.securinest.controlplaneapi.repository.audit.AuditPackRepository;
import io.securinest.controlplaneapi.repository.tenant.TenantMemberRepository;
import io.securinest.controlplaneapi.util.audit.AuditValidationUtils;
import java.time.Instant;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.server.ResponseStatusException;

@Service
@RequiredArgsConstructor
@Slf4j
public class AuditService {

  private final AuditLogEntryRepository auditLogEntryRepository;
  private final AuditPackRepository auditPackRepository;
  private final TenantMemberRepository tenantMemberRepository;
  private final AuditMapper auditMapper;

  private static final Set<Role> AUDIT_LOG_ALLOWED_ROLES = Set.of(Role.OWNER, Role.ADMIN,
      Role.POLICY_ADMIN, Role.AUDITOR);

  // Roles allowed to create/view audit packs
  private static final Set<Role> AUDIT_PACK_ALLOWED_ROLES = Set.of(Role.OWNER, Role.ADMIN,
      Role.AUDITOR);

  /**
   * Get audit logs for a tenant with filtering and pagination
   */
  @Transactional(readOnly = true)
  public PageResponse<AuditLogEntryResponse> getAuditLogs(
      UUID tenantId,
      UUID currentUserId,
      String actionsParam,
      String targetTypesParam,
      String targetIdParam,
      String actorUserIdParam,
      String fromParam,
      String toParam,
      Integer page,
      Integer size,
      String sortParam) {

    log.debug("Getting audit logs for tenant: {} by user: {}", tenantId, currentUserId);

    // Validate membership and role
    TenantMember membership = validateMembershipAndRole(tenantId, currentUserId,
        AUDIT_LOG_ALLOWED_ROLES);

    // Validate and normalize parameters
    List<AuditAction> actions = AuditValidationUtils.validateAndNormalizeActions(actionsParam);
    List<String> targetTypes = AuditValidationUtils.validateAndNormalizeTargetTypes(
        targetTypesParam);
    UUID targetId = AuditValidationUtils.validateUuid(targetIdParam, "targetId");
    UUID actorUserId = AuditValidationUtils.validateUuid(actorUserIdParam, "actorUserId");
    AuditValidationUtils.DateRange dateRange = AuditValidationUtils.validateAndNormalizeDateRange(
        fromParam, toParam);
    AuditValidationUtils.PaginationParams pagination = AuditValidationUtils.validatePagination(page,
        size);
    String sortString = AuditValidationUtils.validateAuditLogSort(sortParam);

    // Build pageable with sort
    Sort sort = buildSort(sortString);
    Pageable pageable = PageRequest.of(pagination.page(), pagination.size(), sort);

    // Query audit logs with filters
    Page<AuditLogEntry> auditLogs = queryAuditLogs(tenantId, actions, targetTypes, targetId,
        actorUserId, dateRange, pageable);

    // Apply row-level redaction if needed
    List<AuditLogEntryResponse> responses = auditLogs.getContent().stream()
        .map(entry -> applyRedactionIfNeeded(entry, membership.getRole()))
        .map(auditMapper::toResponse)
        .toList();

    return new PageResponse<>(
        responses,
        auditLogs.getNumber(),
        auditLogs.getSize(),
        auditLogs.getTotalElements(),
        auditLogs.getTotalPages()
    );
  }

  /**
   * Create an audit pack request
   */
  @Transactional
  public AuditPackResponse createAuditPack(
      UUID tenantId,
      UUID currentUserId,
      AuditPackRequest request,
      String idempotencyKey,
      String requestId) {

    log.debug("Creating audit pack for tenant: {} by user: {}", tenantId, currentUserId);

    // Validate membership and role
    validateMembershipAndRole(tenantId, currentUserId, AUDIT_PACK_ALLOWED_ROLES);

    // Validate request
    validateAuditPackRequest(request, tenantId);

    // Check idempotency if key provided
    if (idempotencyKey != null && !idempotencyKey.trim().isEmpty()) {
      Optional<AuditPack> existing = findRecentAuditPackByIdempotencyKey(tenantId,
          idempotencyKey.trim());
      if (existing.isPresent()) {
        log.info("Returning existing audit pack for idempotency key: {}", idempotencyKey);
        return auditMapper.toResponse(existing.get());
      }
    }

    // Create audit pack record
    AuditPack auditPack = new AuditPack();
    auditPack.setTenantId(tenantId);
    auditPack.setScope(buildScopeMap(request));
    auditPack.setStatus(JobStatus.QUEUED);
    auditPack.setAiActVersion(resolveAiActVersion(request.aiActVersion()));
    auditPack.setCreatedBy(currentUserId);

    AuditPack saved = auditPackRepository.save(auditPack);

    // Create audit log entry
    createAuditLogEntry(tenantId, currentUserId, AuditAction.AUDIT_PACK_QUEUED, "AUDIT_PACK",
        saved.getId(), null);

    // TODO: Enqueue build job (publish to queue or outbox)
    log.info("Audit pack queued: {} for tenant: {} by user: {}", saved.getId(), tenantId,
        currentUserId);

    return auditMapper.toResponse(saved);
  }

  /**
   * List audit packs for a tenant
   */
  @Transactional(readOnly = true)
  public PageResponse<AuditPackResponse> listAuditPacks(
      UUID tenantId,
      UUID currentUserId,
      String statusParam,
      String fromParam,
      String toParam,
      Integer page,
      Integer size,
      String sortParam) {

    log.debug("Listing audit packs for tenant: {} by user: {}", tenantId, currentUserId);

    // Validate membership and role
    validateMembershipAndRole(tenantId, currentUserId, AUDIT_PACK_ALLOWED_ROLES);

    // Validate parameters
    JobStatus status = validateJobStatus(statusParam);
    AuditValidationUtils.DateRange dateRange = AuditValidationUtils.validateAndNormalizeDateRange(
        fromParam, toParam);
    AuditValidationUtils.PaginationParams pagination = AuditValidationUtils.validatePagination(page,
        size);
    String sortString = AuditValidationUtils.validateAuditPackSort(sortParam);

    // Build pageable with sort
    Sort sort = buildSort(sortString);
    Pageable pageable = PageRequest.of(pagination.page(), pagination.size(), sort);

    // Query audit packs
    Page<AuditPack> auditPacks = queryAuditPacks(tenantId, status, dateRange, pageable);

    List<AuditPackResponse> responses = auditPacks.getContent().stream()
        .map(auditMapper::toResponse)
        .toList();

    return new PageResponse<>(
        responses,
        auditPacks.getNumber(),
        auditPacks.getSize(),
        auditPacks.getTotalElements(),
        auditPacks.getTotalPages()
    );
  }

  /**
   * Get a specific audit pack
   */
  @Transactional(readOnly = true)
  public AuditPackResponse getAuditPack(UUID tenantId, UUID packId, UUID currentUserId) {
    log.debug("Getting audit pack: {} for tenant: {} by user: {}", packId, tenantId, currentUserId);

    // Validate membership and role
    validateMembershipAndRole(tenantId, currentUserId, AUDIT_PACK_ALLOWED_ROLES);

    // Find audit pack
    AuditPack auditPack = auditPackRepository.findByIdAndTenantId(packId, tenantId)
        .orElseThrow(
            () -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Audit pack not found"));

    return auditMapper.toResponse(auditPack);
  }

  private TenantMember validateMembershipAndRole(UUID tenantId, UUID currentUserId,
      Set<Role> allowedRoles) {
    if (currentUserId == null) {
      throw new ResponseStatusException(HttpStatus.UNAUTHORIZED, "Missing current user");
    }

    TenantMember membership = tenantMemberRepository.findByTenantIdAndUserId(tenantId,
            currentUserId)
        .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Tenant not found"));

    if (!allowedRoles.contains(membership.getRole())) {
      throw new SecurinestException(HttpStatus.FORBIDDEN, "Insufficient role");
    }

    return membership;
  }

  private Page<AuditLogEntry> queryAuditLogs(
      UUID tenantId,
      List<AuditAction> actions,
      List<String> targetTypes,
      UUID targetId,
      UUID actorUserId,
      AuditValidationUtils.DateRange dateRange,
      Pageable pageable) {

    // For now, use simple repository methods
    // In a real implementation, you'd use Criteria API or custom queries for complex filtering
    return auditLogEntryRepository.findAllByTenantIdAndTsBetweenOrderByTsDesc(
        tenantId, dateRange.from(), dateRange.to(), pageable);
  }

  private Page<AuditPack> queryAuditPacks(
      UUID tenantId,
      JobStatus status,
      AuditValidationUtils.DateRange dateRange,
      Pageable pageable) {

    // For now, use simple repository method
    // In a real implementation, you'd add filtering by status and date range
    return auditPackRepository.findAllByTenantIdOrderByCreatedAtDesc(tenantId, pageable);
  }

  private AuditLogEntry applyRedactionIfNeeded(AuditLogEntry entry, Role userRole) {
    // Apply redaction for sensitive fields based on role
    if (userRole == Role.AUDITOR || userRole == Role.POLICY_ADMIN) {
      // For now, return as-is. In a real implementation, you'd redact sensitive diff fields
      // and set a redacted flag
    }
    return entry;
  }

  private void validateAuditPackRequest(AuditPackRequest request, UUID tenantId) {
    if (request == null) {
      throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Missing body");
    }

    if (request.scope() == null) {
      throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Scope is required");
    }

    // Validate scope has at least one dimension
    boolean hasServices =
        request.scope().services() != null && !request.scope().services().isEmpty();
    boolean hasEnvs = request.scope().envs() != null && !request.scope().envs().isEmpty();

    if (!hasServices || !hasEnvs) {
      throw new ResponseStatusException(HttpStatus.BAD_REQUEST,
          "Scope must specify both services and environments");
    }

    // Validate date range
    if (request.scope().dateRange() == null ||
        request.scope().dateRange().from() == null ||
        request.scope().dateRange().to() == null) {
      throw new ResponseStatusException(HttpStatus.BAD_REQUEST,
          "Date range with 'from' and 'to' is required");
    }

    AuditValidationUtils.validateAuditPackDateRange(
        request.scope().dateRange().from(),
        request.scope().dateRange().to()
    );

    // TODO: Validate that services and envs belong to the tenant
  }

  private Map<String, Object> buildScopeMap(AuditPackRequest request) {
    Map<String, Object> scope = new HashMap<>();
    scope.put("services", request.scope().services());
    scope.put("envs", request.scope().envs());

    Map<String, Object> dateRange = new HashMap<>();
    dateRange.put("from", request.scope().dateRange().from().toString());
    dateRange.put("to", request.scope().dateRange().to().toString());
    scope.put("dateRange", dateRange);

    return scope;
  }

  private String resolveAiActVersion(String requestedVersion) {
    // TODO: Implement version resolution logic
    // For now, return a default version
    return requestedVersion != null ? requestedVersion : "1.0.0";
  }

  private Optional<AuditPack> findRecentAuditPackByIdempotencyKey(UUID tenantId,
      String idempotencyKey) {
    // TODO: Implement idempotency key lookup
    // This would require adding an idempotency_key field to the audit_pack table
    return Optional.empty();
  }

  private void createAuditLogEntry(UUID tenantId, UUID actorUserId, AuditAction action,
      String targetType, UUID targetId, String diff) {
    AuditLogEntry entry = new AuditLogEntry();
    entry.setTenantId(tenantId);
    entry.setActorUserId(actorUserId);
    entry.setAction(action);
    entry.setTargetType(targetType);
    entry.setTargetId(targetId);
    entry.setTs(Instant.now());
    entry.setDiff(diff);

    auditLogEntryRepository.save(entry);
  }

  private JobStatus validateJobStatus(String statusParam) {
    if (statusParam == null || statusParam.trim().isEmpty()) {
      return null;
    }

    try {
      return JobStatus.valueOf(statusParam.trim().toUpperCase());
    } catch (IllegalArgumentException e) {
      throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Invalid status: " + statusParam);
    }
  }

  private Sort buildSort(String sortString) {
    String[] parts = sortString.split(",");
    String field = parts[0];
    String direction = parts[1];

    return Sort.by(direction.equals("DESC") ? Sort.Direction.DESC : Sort.Direction.ASC, field);
  }
}
