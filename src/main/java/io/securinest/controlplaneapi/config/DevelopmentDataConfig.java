package io.securinest.controlplaneapi.config;

import io.securinest.controlplaneapi.entity.policy.SigningKey;
import io.securinest.controlplaneapi.repository.policy.SigningKeyRepository;
import io.securinest.controlplaneapi.service.policy.PolicySigningService;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

@Configuration
@Profile("dev")
@RequiredArgsConstructor
@Slf4j
public class DevelopmentDataConfig {

  private final SigningKeyRepository signingKeyRepository;
  private final PolicySigningService signingService;

  @Bean
  public CommandLineRunner initDevelopmentData() {
    return args -> {
      log.info("Initializing development data...");

      // Create mock signing keys for development
      createMockSigningKeyIfNotExists("11111111-1111-1111-1111-111111111111"); // Mock tenant 1
      createMockSigningKeyIfNotExists("*************-2222-2222-************"); // Mock tenant 2

      log.info("Development data initialization complete");
    };
  }

  private void createMockSigningKeyIfNotExists(String tenantIdStr) {
    UUID tenantId = UUID.fromString(tenantIdStr);

    // Check if signing key already exists for this tenant
    if (!signingKeyRepository.findAllByTenantIdAndRevokedAtIsNull(tenantId).isEmpty()) {
      log.debug("Signing key already exists for tenant: {}", tenantId);
      return;
    }

    // Create mock public key (in production, this would come from KMS)
    String mockPublicKey = generateMockPublicKey(tenantId);
    String kid = signingService.generateKeyFingerprint(mockPublicKey);

    SigningKey signingKey = new SigningKey();
    signingKey.setTenantId(tenantId);
    signingKey.setKid(kid);
    signingKey.setPublicKeyPem(mockPublicKey);

    signingKeyRepository.save(signingKey);

    log.info("Created mock signing key for tenant: {} with kid: {}", tenantId, kid);
  }

  private String generateMockPublicKey(UUID tenantId) {
    // Generate a mock PEM-formatted public key for development
    // In production, this would be a real Ed25519 or RSA public key
    return String.format(
        "-----BEGIN PUBLIC KEY-----\n" +
            "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA%s\n" +
            "QIDAQAB\n" +
            "-----END PUBLIC KEY-----\n",
        tenantId.toString().replace("-", "").substring(0, 32).toUpperCase());
  }
}
