package io.securinest.controlplaneapi.config;

import io.securinest.controlplaneapi.exceptions.shared.SecurinestConfigurationException;
import java.util.HashSet;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;

@ConfigurationProperties(prefix = "tenant.rules")
@Slf4j
public class TenantRulesProperties {

  private final Set<String> normalizedAllowedRegions;
  private final Set<String> normalizedReservedSlugs;

  public TenantRulesProperties(
      Set<String> allowedRegions,
      Set<String> reservedSlugs,
      Set<String> extraReservedSlugs) {

    Set<String> rawAllowedRegions = allowedRegions != null ? allowedRegions : Set.of("us", "eu");
    Set<String> rawReservedSlugs =
        reservedSlugs != null ? reservedSlugs : getDefaultReservedSlugs();
    Set<String> rawExtraReservedSlugs = extraReservedSlugs != null ? extraReservedSlugs : Set.of();

    this.normalizedAllowedRegions = normalizeStringSet(rawAllowedRegions, "allowedRegions");
    if (this.normalizedAllowedRegions.isEmpty()) {
      throw new IllegalStateException("tenant.rules.allowedRegions cannot be empty");
    }

    boolean hasBadRegion = normalizedAllowedRegions.stream()
        .anyMatch(region -> region.chars().anyMatch(Character::isWhitespace));

    if (hasBadRegion) {
      throw SecurinestConfigurationException.forKey(
          "tenant.rules.allowedRegions",
          "contains a region with whitespace"
      );
    }

    Set<String> allReservedSlugs = new HashSet<>(rawReservedSlugs);
    allReservedSlugs.addAll(rawExtraReservedSlugs);
    this.normalizedReservedSlugs = normalizeStringSet(allReservedSlugs, "reservedSlugs");

    log.info("Tenant rules initialized:");
    log.info("  - Allowed regions ({}): {}", this.normalizedAllowedRegions.size(),
        this.normalizedAllowedRegions);
    log.info("  - Reserved slugs: {} total (default: {}, extra: {})",
        this.normalizedReservedSlugs.size(),
        rawReservedSlugs.size(),
        rawExtraReservedSlugs.size());
    log.info("  - First 10 reserved slugs: {}",
        this.normalizedReservedSlugs.stream().limit(10).toList());
  }

  private static Set<String> getDefaultReservedSlugs() {
    return Set.of(
        "admin", "root", "api", "app", "www", "web", "static", "assets", "cdn", "media", "files",
        "uploads", "downloads", "login", "logout", "signin", "signup", "register", "sso", "oauth",
        "oidc", "me", "account", "profile", "settings", "status", "health", "metrics", "actuator",
        "grafana", "kibana", "prometheus", "docs", "blog", "help", "support", "billing", "payments",
        "stripe", "paypal", "webhook", "hooks", "prod", "production", "staging", "sandbox", "dev",
        "qa", "test", "internal", "system", "default", "null", "undefined", "true", "false",
        "securinest", "secure", "control", "control-plane", "m", "mail", "smtp", "ftp", "api-v1"
    );
  }

  private static Set<String> normalizeStringSet(Set<String> input, String fieldName) {
    if (input == null) {
      log.warn("tenant.rules.{} is null, using empty set", fieldName);
      return Set.of();
    }

    return input.stream()
        .filter(Objects::nonNull)
        .map(String::trim)
        .filter(s -> !s.isEmpty())
        .map(String::toLowerCase)
        .collect(Collectors.toUnmodifiableSet());
  }

  public Set<String> getAllowedRegions() {
    return normalizedAllowedRegions;
  }

  public Set<String> getReservedSlugs() {
    return normalizedReservedSlugs;
  }
}
