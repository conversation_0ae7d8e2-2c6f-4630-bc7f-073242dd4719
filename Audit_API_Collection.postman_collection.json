{"info": {"_postman_id": "audit-api-collection", "name": "Securinest Audit API", "description": "Collection for testing the Audit API endpoints", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "baseUrl", "value": "http://localhost:8080", "type": "string"}, {"key": "tenantId", "value": "550e8400-e29b-41d4-a716-446655440010", "type": "string"}, {"key": "adminUserId", "value": "550e8400-e29b-41d4-a716-446655440001", "type": "string"}, {"key": "auditorUserId", "value": "550e8400-e29b-41d4-a716-446655440002", "type": "string"}, {"key": "viewerUserId", "value": "550e8400-e29b-41d4-a716-446655440003", "type": "string"}], "item": [{"name": "<PERSON><PERSON>", "item": [{"name": "Get All Audit Logs (Admin)", "request": {"method": "GET", "header": [{"key": "X-Debug-UserId", "value": "{{adminUserId}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/tenants/{{tenantId}}/audit/logs", "host": ["{{baseUrl}}"], "path": ["v1", "tenants", "{{tenantId}}", "audit", "logs"]}}, "response": []}, {"name": "Get Audit Logs with Filters", "request": {"method": "GET", "header": [{"key": "X-Debug-UserId", "value": "{{auditorUserId}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/tenants/{{tenantId}}/audit/logs?action=TENANT_CREATED,TENANT_UPDATED&targetType=TENANT&page=0&size=10&sort=ts,DESC", "host": ["{{baseUrl}}"], "path": ["v1", "tenants", "{{tenantId}}", "audit", "logs"], "query": [{"key": "action", "value": "TENANT_CREATED,TENANT_UPDATED"}, {"key": "targetType", "value": "TENANT"}, {"key": "page", "value": "0"}, {"key": "size", "value": "10"}, {"key": "sort", "value": "ts,DESC"}]}}, "response": []}, {"name": "Get Audit Logs by Actor", "request": {"method": "GET", "header": [{"key": "X-Debug-UserId", "value": "{{adminUserId}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/tenants/{{tenantId}}/audit/logs?actorUserId={{auditorUserId}}&page=0&size=20", "host": ["{{baseUrl}}"], "path": ["v1", "tenants", "{{tenantId}}", "audit", "logs"], "query": [{"key": "actor<PERSON><PERSON>Id", "value": "{{auditorUserId}}"}, {"key": "page", "value": "0"}, {"key": "size", "value": "20"}]}}, "response": []}, {"name": "Get Audit Logs with Date Range", "request": {"method": "GET", "header": [{"key": "X-Debug-UserId", "value": "{{adminUserId}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/tenants/{{tenantId}}/audit/logs?from=2024-01-01T00:00:00Z&to=2024-01-31T23:59:59Z&page=0&size=50", "host": ["{{baseUrl}}"], "path": ["v1", "tenants", "{{tenantId}}", "audit", "logs"], "query": [{"key": "from", "value": "2024-01-01T00:00:00Z"}, {"key": "to", "value": "2024-01-31T23:59:59Z"}, {"key": "page", "value": "0"}, {"key": "size", "value": "50"}]}}, "response": []}, {"name": "Get Audit Logs - Unauthorized (Viewer)", "request": {"method": "GET", "header": [{"key": "X-Debug-UserId", "value": "{{viewerUserId}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/tenants/{{tenantId}}/audit/logs", "host": ["{{baseUrl}}"], "path": ["v1", "tenants", "{{tenantId}}", "audit", "logs"]}}, "response": []}]}, {"name": "Audit Packs", "item": [{"name": "Create Audit Pack", "request": {"method": "POST", "header": [{"key": "X-Debug-UserId", "value": "{{auditorUserId}}", "type": "text"}, {"key": "X-Request-Id", "value": "req-{{$randomUUID}}", "type": "text"}, {"key": "Idempotency-Key", "value": "idem-{{$randomUUID}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"scope\": {\n    \"services\": [\"550e8400-e29b-41d4-a716-************\", \"550e8400-e29b-41d4-a716-************\"],\n    \"envs\": [\"550e8400-e29b-41d4-a716-************\"],\n    \"dateRange\": {\n      \"from\": \"2024-01-01T00:00:00Z\",\n      \"to\": \"2024-01-31T23:59:59Z\"\n    }\n  },\n  \"aiActVersion\": \"1.0.0\"\n}"}, "url": {"raw": "{{baseUrl}}/v1/tenants/{{tenantId}}/audit/packs", "host": ["{{baseUrl}}"], "path": ["v1", "tenants", "{{tenantId}}", "audit", "packs"]}}, "response": []}, {"name": "Create Audit Pack - Staging Only", "request": {"method": "POST", "header": [{"key": "X-Debug-UserId", "value": "{{adminUserId}}", "type": "text"}, {"key": "X-Request-Id", "value": "req-staging-{{$randomUUID}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"scope\": {\n    \"services\": [\"550e8400-e29b-41d4-a716-************\"],\n    \"envs\": [\"550e8400-e29b-41d4-a716-************\"],\n    \"dateRange\": {\n      \"from\": \"2024-02-01T00:00:00Z\",\n      \"to\": \"2024-02-29T23:59:59Z\"\n    }\n  },\n  \"aiActVersion\": \"1.1.0\"\n}"}, "url": {"raw": "{{baseUrl}}/v1/tenants/{{tenantId}}/audit/packs", "host": ["{{baseUrl}}"], "path": ["v1", "tenants", "{{tenantId}}", "audit", "packs"]}}, "response": []}, {"name": "List All Audit Packs", "request": {"method": "GET", "header": [{"key": "X-Debug-UserId", "value": "{{auditorUserId}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/tenants/{{tenantId}}/audit/packs", "host": ["{{baseUrl}}"], "path": ["v1", "tenants", "{{tenantId}}", "audit", "packs"]}}, "response": []}, {"name": "List Audit Packs - Filter by Status", "request": {"method": "GET", "header": [{"key": "X-Debug-UserId", "value": "{{adminUserId}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/tenants/{{tenantId}}/audit/packs?status=SUCCEEDED&page=0&size=10", "host": ["{{baseUrl}}"], "path": ["v1", "tenants", "{{tenantId}}", "audit", "packs"], "query": [{"key": "status", "value": "SUCCEEDED"}, {"key": "page", "value": "0"}, {"key": "size", "value": "10"}]}}, "response": []}, {"name": "Get Specific Audit Pack", "request": {"method": "GET", "header": [{"key": "X-Debug-UserId", "value": "{{auditorUserId}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/tenants/{{tenantId}}/audit/packs/550e8400-e29b-41d4-a716-446655440070", "host": ["{{baseUrl}}"], "path": ["v1", "tenants", "{{tenantId}}", "audit", "packs", "550e8400-e29b-41d4-a716-446655440070"]}}, "response": []}, {"name": "Get Audit Pack - Not Found", "request": {"method": "GET", "header": [{"key": "X-Debug-UserId", "value": "{{adminUserId}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/tenants/{{tenantId}}/audit/packs/550e8400-e29b-41d4-a716-446655440999", "host": ["{{baseUrl}}"], "path": ["v1", "tenants", "{{tenantId}}", "audit", "packs", "550e8400-e29b-41d4-a716-446655440999"]}}, "response": []}]}, {"name": "Erro<PERSON>", "item": [{"name": "<PERSON><PERSON> - <PERSON>th Header", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/v1/tenants/{{tenantId}}/audit/logs", "host": ["{{baseUrl}}"], "path": ["v1", "tenants", "{{tenantId}}", "audit", "logs"]}}, "response": []}, {"name": "Audit Logs - Invalid Date Range", "request": {"method": "GET", "header": [{"key": "X-Debug-UserId", "value": "{{adminUserId}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/tenants/{{tenantId}}/audit/logs?from=2024-01-31T00:00:00Z&to=2024-01-01T00:00:00Z", "host": ["{{baseUrl}}"], "path": ["v1", "tenants", "{{tenantId}}", "audit", "logs"], "query": [{"key": "from", "value": "2024-01-31T00:00:00Z"}, {"key": "to", "value": "2024-01-01T00:00:00Z"}]}}, "response": []}, {"name": "Create Audit Pack - <PERSON><PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "X-Debug-UserId", "value": "{{auditorUserId}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"scope\": {\n    \"services\": [],\n    \"envs\": [],\n    \"dateRange\": {\n      \"from\": \"2024-01-01T00:00:00Z\",\n      \"to\": \"2024-01-31T23:59:59Z\"\n    }\n  }\n}"}, "url": {"raw": "{{baseUrl}}/v1/tenants/{{tenantId}}/audit/packs", "host": ["{{baseUrl}}"], "path": ["v1", "tenants", "{{tenantId}}", "audit", "packs"]}}, "response": []}, {"name": "Create Audit Pack - Viewer Role (Forbidden)", "request": {"method": "POST", "header": [{"key": "X-Debug-UserId", "value": "{{viewerUserId}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"scope\": {\n    \"services\": [\"550e8400-e29b-41d4-a716-************\"],\n    \"envs\": [\"550e8400-e29b-41d4-a716-************\"],\n    \"dateRange\": {\n      \"from\": \"2024-01-01T00:00:00Z\",\n      \"to\": \"2024-01-31T23:59:59Z\"\n    }\n  }\n}"}, "url": {"raw": "{{baseUrl}}/v1/tenants/{{tenantId}}/audit/packs", "host": ["{{baseUrl}}"], "path": ["v1", "tenants", "{{tenantId}}", "audit", "packs"]}}, "response": []}]}]}