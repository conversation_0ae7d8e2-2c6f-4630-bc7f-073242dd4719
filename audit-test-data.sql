-- Test data for Audit functionality
-- Run this after the main schema is created

-- Insert test user accounts
INSERT INTO securinest.user_account (id, kc_sub, email, display_name, created_at, updated_at, version) VALUES
('550e8400-e29b-41d4-a716-************', 'test-user-1', '<EMAIL>', 'Admin User', NOW(), NOW(), 0),
('550e8400-e29b-41d4-a716-************', 'test-user-2', '<EMAIL>', 'Auditor User', NOW(), NOW(), 0),
('550e8400-e29b-41d4-a716-************', 'test-user-3', '<EMAIL>', 'Viewer User', NOW(), NOW(), 0);

-- Insert test tenant
INSERT INTO securinest.tenant (id, name, slug, billing_plan, region, created_at, updated_at, version) VALUES
('550e8400-e29b-41d4-a716-************', 'Test Company', 'test-company', 'enterprise', 'us', NOW(), NOW(), 0);

-- Insert tenant memberships with different roles
INSERT INTO securinest.tenant_member (tenant_id, user_id, role, created_at) VALUES
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'OWNER', NOW()),
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'AUDITOR', NOW()),
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'VIEWER', NOW());

-- Insert test environments
INSERT INTO securinest.environment (id, tenant_id, name, slug, created_at, updated_at, version) VALUES
('550e8400-e29b-41d4-a716-446655440020', '550e8400-e29b-41d4-a716-************', 'Production', 'prod', NOW(), NOW(), 0),
('550e8400-e29b-41d4-a716-446655440021', '550e8400-e29b-41d4-a716-************', 'Staging', 'staging', NOW(), NOW(), 0);

-- Insert test services
INSERT INTO securinest.app_service (id, tenant_id, name, slug, created_at, updated_at, version) VALUES
('550e8400-e29b-41d4-a716-446655440030', '550e8400-e29b-41d4-a716-************', 'User API', 'user-api', NOW(), NOW(), 0),
('550e8400-e29b-41d4-a716-446655440031', '550e8400-e29b-41d4-a716-************', 'Payment Service', 'payment-svc', NOW(), NOW(), 0);

-- Insert test audit log entries with various actions and target types
INSERT INTO securinest.audit_log_entry (id, tenant_id, actor_user_id, action, target_type, target_id, ts, diff, created_at) VALUES
-- Tenant lifecycle events
(1, '550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'TENANT_CREATED', 'TENANT', '550e8400-e29b-41d4-a716-************', NOW() - INTERVAL '30 days', '{"name": "Test Company", "region": "us"}', NOW() - INTERVAL '30 days'),
(2, '550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'TENANT_UPDATED', 'TENANT', '550e8400-e29b-41d4-a716-************', NOW() - INTERVAL '25 days', '{"billing_plan": {"old": "free", "new": "enterprise"}}', NOW() - INTERVAL '25 days'),

-- Member management events
(3, '550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'MEMBER_ADDED', 'TENANT_MEMBER', '550e8400-e29b-41d4-a716-************', NOW() - INTERVAL '20 days', '{"email": "<EMAIL>", "role": "AUDITOR"}', NOW() - INTERVAL '20 days'),
(4, '550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'MEMBER_ROLE_CHANGED', 'TENANT_MEMBER', '550e8400-e29b-41d4-a716-************', NOW() - INTERVAL '18 days', '{"role": {"old": "ADMIN", "new": "VIEWER"}}', NOW() - INTERVAL '18 days'),

-- Environment events
(5, '550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'ENV_CREATED', 'ENVIRONMENT', '550e8400-e29b-41d4-a716-446655440020', NOW() - INTERVAL '15 days', '{"name": "Production", "slug": "prod"}', NOW() - INTERVAL '15 days'),
(6, '550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'ENV_CREATED', 'ENVIRONMENT', '550e8400-e29b-41d4-a716-446655440021', NOW() - INTERVAL '15 days', '{"name": "Staging", "slug": "staging"}', NOW() - INTERVAL '15 days'),

-- Service events
(7, '550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'SERVICE_CREATED', 'SERVICE', '550e8400-e29b-41d4-a716-446655440030', NOW() - INTERVAL '12 days', '{"name": "User API", "slug": "user-api"}', NOW() - INTERVAL '12 days'),
(8, '550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'SERVICE_CREATED', 'SERVICE', '550e8400-e29b-41d4-a716-446655440031', NOW() - INTERVAL '10 days', '{"name": "Payment Service", "slug": "payment-svc"}', NOW() - INTERVAL '10 days'),

-- Policy events
(9, '550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'POLICY_CREATED', 'POLICY', '550e8400-e29b-41d4-a716-446655440040', NOW() - INTERVAL '8 days', '{"name": "Data Protection Policy", "state": "DRAFT"}', NOW() - INTERVAL '8 days'),
(10, '550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'POLICY_VERSION_ACTIVATED', 'POLICY_VERSION', '550e8400-e29b-41d4-a716-446655440041', NOW() - INTERVAL '7 days', '{"version": 1, "status": "ACTIVE"}', NOW() - INTERVAL '7 days'),

-- Bundle events
(11, '550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'POLICY_BUNDLE_COMPILED', 'BUNDLE', '550e8400-e29b-41d4-a716-446655440050', NOW() - INTERVAL '5 days', '{"service_id": "550e8400-e29b-41d4-a716-446655440030", "env_id": "550e8400-e29b-41d4-a716-446655440020"}', NOW() - INTERVAL '5 days'),
(12, '550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'POLICY_BUNDLE_ACTIVATED', 'BUNDLE', '550e8400-e29b-41d4-a716-446655440050', NOW() - INTERVAL '5 days', '{"enforcement_mode": "ENFORCE"}', NOW() - INTERVAL '5 days'),

-- API Key events
(13, '550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'SERVICE_API_KEY_CREATED', 'API_KEY', '550e8400-e29b-41d4-a716-446655440060', NOW() - INTERVAL '3 days', '{"name": "Production API Key", "service_id": "550e8400-e29b-41d4-a716-446655440030"}', NOW() - INTERVAL '3 days'),
(14, '550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'SERVICE_API_KEY_ROTATED', 'API_KEY', '550e8400-e29b-41d4-a716-446655440060', NOW() - INTERVAL '1 day', '{"rotated_at": "2024-01-15T10:30:00Z"}', NOW() - INTERVAL '1 day'),

-- Recent audit pack events
(15, '550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'AUDIT_PACK_QUEUED', 'AUDIT_PACK', '550e8400-e29b-41d4-a716-446655440070', NOW() - INTERVAL '2 hours', '{"scope": "production_services", "date_range": "30_days"}', NOW() - INTERVAL '2 hours'),
(16, '550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'AUDIT_PACK_SUCCEEDED', 'AUDIT_PACK', '550e8400-e29b-41d4-a716-446655440070', NOW() - INTERVAL '1 hour', '{"manifest_hash": "sha256:abc123...", "artifact_size": "2.5MB"}', NOW() - INTERVAL '1 hour');

-- Insert test audit packs with different statuses
INSERT INTO securinest.audit_pack (id, tenant_id, scope, status, manifest_hash, artifact_uri, ai_act_version, created_by, created_at, updated_at, version, finished_at, signature_b64) VALUES
-- Completed audit pack
('550e8400-e29b-41d4-a716-446655440070', '550e8400-e29b-41d4-a716-************', 
 '{"services": ["550e8400-e29b-41d4-a716-446655440030", "550e8400-e29b-41d4-a716-446655440031"], "envs": ["550e8400-e29b-41d4-a716-446655440020"], "dateRange": {"from": "2024-01-01T00:00:00Z", "to": "2024-01-31T23:59:59Z"}}',
 'SUCCEEDED', 'sha256:abc123def456...', 'https://storage.example.com/audit-packs/pack-70.zip', '1.0.0', '550e8400-e29b-41d4-a716-************', 
 NOW() - INTERVAL '2 hours', NOW() - INTERVAL '1 hour', 0, NOW() - INTERVAL '1 hour', 'signature_base64_string_here'),

-- Running audit pack
('550e8400-e29b-41d4-a716-446655440071', '550e8400-e29b-41d4-a716-************',
 '{"services": ["550e8400-e29b-41d4-a716-446655440030"], "envs": ["550e8400-e29b-41d4-a716-446655440021"], "dateRange": {"from": "2024-02-01T00:00:00Z", "to": "2024-02-28T23:59:59Z"}}',
 'RUNNING', NULL, NULL, '1.0.0', '550e8400-e29b-41d4-a716-************',
 NOW() - INTERVAL '30 minutes', NOW() - INTERVAL '30 minutes', 0, NULL, NULL),

-- Queued audit pack
('550e8400-e29b-41d4-a716-446655440072', '550e8400-e29b-41d4-a716-************',
 '{"services": ["550e8400-e29b-41d4-a716-446655440031"], "envs": ["550e8400-e29b-41d4-a716-446655440020", "550e8400-e29b-41d4-a716-446655440021"], "dateRange": {"from": "2024-03-01T00:00:00Z", "to": "2024-03-15T23:59:59Z"}}',
 'QUEUED', NULL, NULL, '1.1.0', '550e8400-e29b-41d4-a716-************',
 NOW() - INTERVAL '5 minutes', NOW() - INTERVAL '5 minutes', 0, NULL, NULL),

-- Failed audit pack
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************',
 '{"services": ["550e8400-e29b-41d4-a716-446655440030"], "envs": ["550e8400-e29b-41d4-a716-446655440020"], "dateRange": {"from": "2023-12-01T00:00:00Z", "to": "2023-12-31T23:59:59Z"}}',
 'FAILED', NULL, NULL, '1.0.0', '550e8400-e29b-41d4-a716-************',
 NOW() - INTERVAL '1 day', NOW() - INTERVAL '23 hours', 0, NOW() - INTERVAL '23 hours', NULL);

-- Insert additional audit log entries for the failed pack
INSERT INTO securinest.audit_log_entry (id, tenant_id, actor_user_id, action, target_type, target_id, ts, diff, created_at) VALUES
(17, '550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'AUDIT_PACK_QUEUED', 'AUDIT_PACK', '550e8400-e29b-41d4-a716-************', NOW() - INTERVAL '1 day', '{"scope": "december_data"}', NOW() - INTERVAL '1 day'),
(18, '550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'AUDIT_PACK_FAILED', 'AUDIT_PACK', '550e8400-e29b-41d4-a716-************', NOW() - INTERVAL '23 hours', '{"error": "Insufficient data coverage: 85% (required: 95%)"}', NOW() - INTERVAL '23 hours');

-- Verify the test data
SELECT 'User Accounts' as table_name, COUNT(*) as count FROM securinest.user_account WHERE id LIKE '550e8400-e29b-41d4-a716-***********%'
UNION ALL
SELECT 'Tenants', COUNT(*) FROM securinest.tenant WHERE id = '550e8400-e29b-41d4-a716-************'
UNION ALL
SELECT 'Tenant Members', COUNT(*) FROM securinest.tenant_member WHERE tenant_id = '550e8400-e29b-41d4-a716-************'
UNION ALL
SELECT 'Environments', COUNT(*) FROM securinest.environment WHERE tenant_id = '550e8400-e29b-41d4-a716-************'
UNION ALL
SELECT 'Services', COUNT(*) FROM securinest.app_service WHERE tenant_id = '550e8400-e29b-41d4-a716-************'
UNION ALL
SELECT 'Audit Log Entries', COUNT(*) FROM securinest.audit_log_entry WHERE tenant_id = '550e8400-e29b-41d4-a716-************'
UNION ALL
SELECT 'Audit Packs', COUNT(*) FROM securinest.audit_pack WHERE tenant_id = '550e8400-e29b-41d4-a716-************';
